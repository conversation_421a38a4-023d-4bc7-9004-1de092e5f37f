#!/usr/bin/env python3
"""
Simple AI Agent Training Script
Easy-to-use training with visualization and memory
"""

import asyncio
import logging
import random
import time
from datetime import datetime
from colorama import init, Fore, Style

# Initialize colorama
init()

def print_banner():
    """Print training banner"""
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🤖 SIMPLE AI AGENT TRAINING{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Memory + Visualization + ₹1000 Budget{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")

async def run_simple_training(num_predictions=10):
    """Run simple training session"""
    
    print(f"\n{Fore.YELLOW}🚀 Starting Training Session{Style.RESET_ALL}")
    print(f"Target: {num_predictions} predictions")
    print(f"Budget: ₹1000")
    
    try:
        # Import systems
        from environment.live_prediction_env import live_env, PredictionAction
        from portfolio.portfolio_manager import portfolio_manager
        from memory.persistent_memory import persistent_memory
        from data.market_data_fetcher import get_market_data_fetcher
        
        # Start environment
        print(f"\n{Fore.GREEN}🔧 Initializing systems...{Style.RESET_ALL}")
        await live_env.start_live_environment()
        
        # Training symbols
        symbols = ["RELIANCE.NS", "TCS.NS", "HDFCBANK.NS", "INFY.NS", "ICICIBANK.NS"]
        
        predictions_made = 0
        start_time = datetime.now()
        
        print(f"\n{Fore.GREEN}✅ Training started!{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Making {num_predictions} predictions...{Style.RESET_ALL}")
        
        # Training loop
        while predictions_made < num_predictions:
            try:
                # Select random symbol
                symbol = random.choice(symbols)
                
                # Generate prediction parameters
                confidence = random.uniform(0.6, 0.95)
                predicted_change = random.uniform(-2.5, 2.5)
                
                # Determine action
                if predicted_change > 1.0:
                    action = PredictionAction.BUY
                elif predicted_change > 0.5:
                    action = PredictionAction.BUY
                elif predicted_change < -1.0:
                    action = PredictionAction.SELL
                elif predicted_change < -0.5:
                    action = PredictionAction.SELL
                else:
                    action = PredictionAction.HOLD
                
                # Make prediction
                pred_id = live_env.make_prediction(
                    symbol=symbol,
                    action=action,
                    confidence=confidence,
                    predicted_change=predicted_change,
                    timeframe=2,  # 2 minutes
                    reasoning=f"Training prediction {predictions_made + 1}"
                )
                
                if pred_id:
                    predictions_made += 1
                    print(f"📊 Prediction {predictions_made}: {symbol} | "
                          f"{action.name} | Confidence: {confidence:.1%}")
                    
                    # Show progress every 5 predictions
                    if predictions_made % 5 == 0:
                        await show_progress(predictions_made, num_predictions)
                
                # Wait between predictions
                await asyncio.sleep(30)  # 30 seconds
                
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}Training stopped by user{Style.RESET_ALL}")
                break
            except Exception as e:
                print(f"⚠️  Prediction error: {e}")
                await asyncio.sleep(5)
        
        # Training completed
        await training_summary(predictions_made, start_time)
        
    except Exception as e:
        print(f"{Fore.RED}❌ Training error: {e}{Style.RESET_ALL}")
    finally:
        # Stop environment
        try:
            live_env.stop()
        except:
            pass

async def show_progress(current, target):
    """Show training progress"""
    try:
        from portfolio.portfolio_manager import portfolio_manager
        from memory.persistent_memory import persistent_memory
        
        progress = (current / target) * 100
        print(f"\n{Fore.BLUE}📊 Progress: {progress:.1f}% ({current}/{target}){Style.RESET_ALL}")
        
        # Portfolio status
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        print(f"💰 Portfolio: ₹{portfolio_summary['total_value']:,.2f} | "
              f"P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
        
        # Memory status
        experiences = persistent_memory.experiences
        if experiences:
            recent_accuracy = sum(exp.accuracy_score for exp in experiences[-5:]) / min(5, len(experiences))
            print(f"🧠 Recent Accuracy: {recent_accuracy:.1%}")
        
    except Exception as e:
        print(f"⚠️  Progress error: {e}")

async def training_summary(predictions_made, start_time):
    """Show training summary"""
    
    print(f"\n{Fore.GREEN}{'='*50}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🎉 TRAINING COMPLETED!{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*50}{Style.RESET_ALL}")
    
    # Duration
    duration = datetime.now() - start_time
    print(f"\n⏱️  Duration: {duration.total_seconds()/60:.1f} minutes")
    print(f"🎯 Predictions Made: {predictions_made}")
    
    try:
        # Portfolio summary
        from portfolio.portfolio_manager import portfolio_manager
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        
        print(f"\n💰 PORTFOLIO RESULTS:")
        print(f"💵 Final Value: ₹{portfolio_summary['total_value']:,.2f}")
        print(f"📈 Return: {portfolio_summary['total_return_pct']:+.2f}%")
        print(f"💹 P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
        print(f"🎯 Trades: {portfolio_summary['total_trades']}")
        
        # Memory summary
        from memory.persistent_memory import persistent_memory
        experiences = persistent_memory.experiences
        
        print(f"\n🧠 LEARNING RESULTS:")
        print(f"📚 Total Experiences: {len(experiences)}")
        
        if experiences:
            accuracy = sum(exp.accuracy_score for exp in experiences) / len(experiences)
            print(f"🎯 Overall Accuracy: {accuracy:.1%}")
        
        # Generate visualizations
        print(f"\n📊 Generating visualizations...")
        from visualization.trading_visualizer import trading_visualizer
        
        # Create charts
        trading_visualizer.create_portfolio_performance_chart()
        summary_path = trading_visualizer.save_daily_summary_chart()
        
        print(f"✅ Charts created!")
        
        # Performance assessment
        if portfolio_summary['total_return_pct'] > 5:
            print(f"\n{Fore.GREEN}🎉 Excellent performance! Agent is learning well!{Style.RESET_ALL}")
        elif portfolio_summary['total_return_pct'] > 0:
            print(f"\n{Fore.YELLOW}📈 Positive returns! Keep training!{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.BLUE}📊 Learning in progress. More training recommended!{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"⚠️  Summary error: {e}")

def show_menu():
    """Show training menu"""
    print(f"\n{Fore.CYAN}🎯 TRAINING OPTIONS:{Style.RESET_ALL}")
    print("1. 🚀 Quick Training (10 predictions)")
    print("2. 📈 Standard Training (25 predictions)")
    print("3. 🏆 Extended Training (50 predictions)")
    print("4. 📊 Check Current Status")
    print("5. 🧠 View Memory Report")
    print("6. 💰 Portfolio Status")
    print("7. 📈 Open Visualizations")
    print("8. ❓ Help")
    print("9. 🚪 Exit")

def check_status():
    """Check current status"""
    try:
        from portfolio.portfolio_manager import portfolio_manager
        from memory.persistent_memory import persistent_memory
        
        print(f"\n{Fore.YELLOW}📊 CURRENT STATUS{Style.RESET_ALL}")
        
        # Portfolio
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        print(f"\n💰 Portfolio: ₹{portfolio_summary['total_value']:,.2f}")
        print(f"📈 Return: {portfolio_summary['total_return_pct']:+.2f}%")
        print(f"🎯 Trades: {portfolio_summary['total_trades']}")
        
        # Memory
        experiences = persistent_memory.experiences
        print(f"\n🧠 Experiences: {len(experiences)}")
        
        if experiences:
            accuracy = sum(exp.accuracy_score for exp in experiences) / len(experiences)
            print(f"🎯 Accuracy: {accuracy:.1%}")
        
    except Exception as e:
        print(f"❌ Status error: {e}")

def view_memory_report():
    """View memory report"""
    try:
        from memory.memory_visualizer import memory_visualizer
        
        print(f"\n{Fore.YELLOW}🧠 Generating Memory Report...{Style.RESET_ALL}")
        
        report = memory_visualizer.generate_memory_report()
        
        # Show summary
        summary = report.get('summary', {})
        print(f"\n📊 Memory Summary:")
        print(f"📚 Experiences: {summary.get('total_experiences', 0)}")
        print(f"🎯 Accuracy: {summary.get('overall_accuracy', 0):.1%}")
        
        # Recommendations
        recommendations = report.get('recommendations', [])
        if recommendations:
            print(f"\n💡 AI Recommendations:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"{i}. {rec}")
        
        print(f"✅ Full report saved!")
        
    except Exception as e:
        print(f"❌ Memory report error: {e}")

def open_visualizations():
    """Open visualization files"""
    try:
        import webbrowser
        from pathlib import Path
        
        chart_dir = Path("visualization/charts")
        
        if not chart_dir.exists():
            print(f"❌ No visualization files found. Run training first!")
            return
        
        html_files = list(chart_dir.glob("*.html"))
        
        if not html_files:
            print(f"❌ No chart files found. Run training first!")
            return
        
        # Open latest file
        latest = max(html_files, key=lambda x: x.stat().st_mtime)
        webbrowser.open(f"file://{latest.absolute()}")
        
        print(f"🌐 Opened: {latest.name}")
        
    except Exception as e:
        print(f"❌ Visualization error: {e}")

def show_help():
    """Show help information"""
    print(f"\n{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📚 TRAINING HELP{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*50}{Style.RESET_ALL}")
    
    print(f"\n{Fore.GREEN}🎯 How Training Works:{Style.RESET_ALL}")
    print("1. AI makes predictions on real stock data")
    print("2. Predictions are tracked and resolved")
    print("3. Successful predictions trigger trades")
    print("4. Every experience is stored in memory")
    print("5. AI learns and improves over time")
    
    print(f"\n{Fore.GREEN}📊 Training Options:{Style.RESET_ALL}")
    print("• Quick: 10 predictions (~5 minutes)")
    print("• Standard: 25 predictions (~15 minutes)")
    print("• Extended: 50 predictions (~30 minutes)")
    
    print(f"\n{Fore.GREEN}💰 Budget Management:{Style.RESET_ALL}")
    print("• Starts with ₹1000 virtual budget")
    print("• Only confident predictions trigger trades")
    print("• Risk management prevents losses")
    print("• Real-time P&L tracking")

async def main():
    """Main training function"""
    print_banner()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    while True:
        show_menu()
        
        try:
            choice = input(f"\n{Fore.CYAN}Choose option (1-9): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                await run_simple_training(10)
            elif choice == "2":
                await run_simple_training(25)
            elif choice == "3":
                await run_simple_training(50)
            elif choice == "4":
                check_status()
            elif choice == "5":
                view_memory_report()
            elif choice == "6":
                check_status()
            elif choice == "7":
                open_visualizations()
            elif choice == "8":
                show_help()
            elif choice == "9":
                print(f"\n{Fore.YELLOW}🎓 Happy training! Your progress is saved!{Style.RESET_ALL}")
                break
            else:
                print(f"{Fore.RED}❌ Invalid choice. Please enter 1-9.{Style.RESET_ALL}")
        
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}🎓 Training ended. Progress saved!{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    asyncio.run(main())
