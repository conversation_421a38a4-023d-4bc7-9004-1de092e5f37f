"""
Demo script for Trading Visualization System
Shows real stock graphs, predictions, and P&L tracking with ₹1000 budget
"""

import asyncio
import logging
from datetime import datetime, timedelta
from colorama import init, Fore, Style
import random
import time

# Initialize colorama
init()

# Import visualization components
from visualization.trading_visualizer import trading_visualizer
from portfolio.portfolio_manager import portfolio_manager
from data.market_data_fetcher import get_market_data_fetcher
from environment.live_prediction_env import live_env, PredictionAction

async def demo_portfolio_system():
    """Demonstrate the portfolio management system"""
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}💰 PORTFOLIO MANAGEMENT SYSTEM DEMO{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Starting with ₹1000 budget{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    
    # Show initial portfolio state
    print(f"\n{Fore.GREEN}📊 Initial Portfolio State{Style.RESET_ALL}")
    summary = portfolio_manager.get_portfolio_summary()
    print(f"Initial Capital: ₹{summary['initial_capital']:,.2f}")
    print(f"Cash Balance: ₹{summary['cash_balance']:,.2f}")
    print(f"Total Value: ₹{summary['total_value']:,.2f}")
    print(f"Total Trades: {summary['total_trades']}")
    
    # Simulate some trades
    print(f"\n{Fore.YELLOW}🎯 Simulating AI Trading Decisions...{Style.RESET_ALL}")
    
    # Get some real market data
    fetcher = get_market_data_fetcher()
    test_symbols = ["RELIANCE.NS", "TCS.NS", "HDFCBANK.NS"]
    
    for i, symbol in enumerate(test_symbols):
        try:
            print(f"\n📊 Processing {symbol}...")
            
            # Get real market data
            market_data = await fetcher.fetch_realtime_data(symbol)
            if not market_data:
                print(f"❌ No data for {symbol}")
                continue
            
            current_price = market_data.close
            print(f"Current Price: ₹{current_price:.2f}")
            
            # Simulate AI prediction with varying confidence
            confidence = random.uniform(0.6, 0.9)
            action = random.choice([PredictionAction.BUY, PredictionAction.SELL, PredictionAction.HOLD])
            
            print(f"AI Prediction: {action.name} (Confidence: {confidence:.1%})")
            
            # Execute trade based on prediction
            if action in [PredictionAction.BUY, PredictionAction.STRONG_BUY]:
                can_buy, quantity, message = portfolio_manager.can_buy(symbol, current_price, confidence)
                print(f"Buy Check: {message}")
                
                if can_buy:
                    trade = portfolio_manager.execute_buy(
                        symbol=symbol,
                        price=current_price,
                        confidence=confidence,
                        reasoning=f"Demo AI prediction: {action.name}"
                    )
                    if trade:
                        print(f"✅ BOUGHT: {trade.quantity} shares for ₹{trade.amount:.2f}")
            
            elif action in [PredictionAction.SELL, PredictionAction.STRONG_SELL]:
                can_sell, quantity, message = portfolio_manager.can_sell(symbol)
                print(f"Sell Check: {message}")
                
                if can_sell:
                    trade = portfolio_manager.execute_sell(
                        symbol=symbol,
                        price=current_price,
                        confidence=confidence,
                        reasoning=f"Demo AI prediction: {action.name}"
                    )
                    if trade:
                        print(f"✅ SOLD: {trade.quantity} shares for ₹{trade.amount:.2f}")
            
            # Update portfolio with current prices
            portfolio_manager.update_positions({symbol: current_price})
            
            # Small delay between trades
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ Error processing {symbol}: {e}")
    
    # Show final portfolio state
    print(f"\n{Fore.GREEN}📊 Final Portfolio State{Style.RESET_ALL}")
    final_summary = portfolio_manager.get_portfolio_summary()
    
    print(f"Cash Balance: ₹{final_summary['cash_balance']:,.2f}")
    print(f"Positions Value: ₹{final_summary['positions_value']:,.2f}")
    print(f"Total Value: ₹{final_summary['total_value']:,.2f}")
    print(f"Total P&L: ₹{final_summary['total_pnl']:+,.2f}")
    print(f"Return: {final_summary['total_return_pct']:+.2f}%")
    print(f"Total Trades: {final_summary['total_trades']}")
    print(f"Win Rate: {final_summary['win_rate']:.1f}%")
    
    # Show positions
    positions = final_summary.get('positions', {})
    if positions:
        print(f"\n{Fore.BLUE}📈 Current Positions{Style.RESET_ALL}")
        for symbol, pos in positions.items():
            print(f"{symbol}: {pos['quantity']} shares @ ₹{pos['avg_buy_price']:.2f} "
                  f"(Current: ₹{pos['current_price']:.2f}, "
                  f"P&L: ₹{pos['unrealized_pnl']:+.2f})")
    
    # Show recent trades
    recent_trades = final_summary.get('recent_trades', [])
    if recent_trades:
        print(f"\n{Fore.MAGENTA}📋 Recent Trades{Style.RESET_ALL}")
        for trade in recent_trades[-3:]:  # Last 3 trades
            print(f"{trade['action']}: {trade['quantity']} {trade['symbol']} @ ₹{trade['price']:.2f}")

async def demo_visualization_system():
    """Demonstrate the visualization system"""
    print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📊 VISUALIZATION SYSTEM DEMO{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    
    try:
        # Create stock chart with predictions
        print(f"\n{Fore.GREEN}📈 Creating Stock Charts...{Style.RESET_ALL}")
        
        test_symbols = ["RELIANCE.NS", "TCS.NS"]
        
        for symbol in test_symbols:
            print(f"Creating chart for {symbol}...")
            
            # Create detailed stock chart
            fig = trading_visualizer.create_stock_chart_with_predictions(symbol)
            if fig:
                print(f"✅ Chart created for {symbol}")
            else:
                print(f"❌ Failed to create chart for {symbol}")
        
        # Create portfolio performance chart
        print(f"\n{Fore.GREEN}💰 Creating Portfolio Performance Chart...{Style.RESET_ALL}")
        
        # Add some sample data to visualizer
        for i in range(10):
            portfolio_summary = portfolio_manager.get_portfolio_summary()
            trading_visualizer.portfolio_data.append({
                'timestamp': datetime.now() - timedelta(minutes=i*5),
                'total_value': portfolio_summary['total_value'] + random.uniform(-50, 50),
                'cash_balance': portfolio_summary['cash_balance'],
                'pnl': portfolio_summary['total_pnl'] + random.uniform(-20, 20)
            })
        
        perf_fig = trading_visualizer.create_portfolio_performance_chart()
        if perf_fig:
            print(f"✅ Portfolio performance chart created")
        
        # Create live dashboard
        print(f"\n{Fore.GREEN}🎛️  Creating Live Dashboard...{Style.RESET_ALL}")
        
        dashboard_fig = trading_visualizer.create_live_dashboard(test_symbols)
        if dashboard_fig:
            print(f"✅ Live dashboard created")
        
        # Save daily summary
        print(f"\n{Fore.GREEN}📋 Creating Daily Summary...{Style.RESET_ALL}")
        
        summary_path = trading_visualizer.save_daily_summary_chart()
        if summary_path:
            print(f"✅ Daily summary saved: {summary_path}")
        
        # Show visualization files created
        print(f"\n{Fore.BLUE}📁 Visualization Files Created{Style.RESET_ALL}")
        viz_dir = trading_visualizer.viz_dir
        
        if viz_dir.exists():
            files = list(viz_dir.glob("*"))
            for file in files[-5:]:  # Show last 5 files
                print(f"📊 {file.name}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error in visualization demo: {e}{Style.RESET_ALL}")

async def demo_live_trading_simulation():
    """Demonstrate live trading simulation with visualization"""
    print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🚀 LIVE TRADING SIMULATION{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Real-time predictions with portfolio execution{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    
    try:
        # Start live environment
        print(f"\n{Fore.YELLOW}🎯 Starting Live Prediction Environment...{Style.RESET_ALL}")
        await live_env.start_live_environment()
        
        # Simulate some predictions
        test_symbols = ["RELIANCE.NS", "TCS.NS"]
        
        for i in range(3):  # Make 3 predictions
            for symbol in test_symbols:
                try:
                    print(f"\n📊 Making prediction {i+1} for {symbol}...")
                    
                    # Get market data
                    fetcher = get_market_data_fetcher()
                    market_data = await fetcher.fetch_realtime_data(symbol)
                    
                    if not market_data:
                        continue
                    
                    # Simulate AI prediction
                    action = random.choice(list(PredictionAction))
                    confidence = random.uniform(0.5, 0.9)
                    predicted_change = random.uniform(-2.0, 2.0)
                    
                    # Make prediction
                    pred_id = live_env.make_prediction(
                        symbol=symbol,
                        action=action,
                        confidence=confidence,
                        predicted_change=predicted_change,
                        timeframe=2,  # 2 minutes for demo
                        reasoning=f"Demo prediction {i+1}: {action.name}"
                    )
                    
                    if pred_id:
                        print(f"✅ Prediction made: {action.name} (confidence: {confidence:.1%})")
                    
                except Exception as e:
                    print(f"❌ Error making prediction for {symbol}: {e}")
            
            # Wait a bit between prediction rounds
            print(f"⏳ Waiting 30 seconds for predictions to resolve...")
            await asyncio.sleep(30)
        
        # Wait for predictions to resolve
        print(f"\n⏳ Waiting for final predictions to resolve...")
        await asyncio.sleep(60)
        
        # Show final results
        print(f"\n{Fore.GREEN}📊 Final Trading Results{Style.RESET_ALL}")
        
        # Portfolio summary
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        print(f"\n💰 Portfolio Performance:")
        print(f"Initial Capital: ₹{portfolio_summary['initial_capital']:,.2f}")
        print(f"Final Value: ₹{portfolio_summary['total_value']:,.2f}")
        print(f"Total P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
        print(f"Return: {portfolio_summary['total_return_pct']:+.2f}%")
        
        # Prediction performance
        performance = live_env.get_performance_summary()
        print(f"\n🎯 Prediction Performance:")
        print(f"Total Predictions: {performance['metrics']['total_predictions']}")
        print(f"Accuracy Rate: {performance['metrics']['accuracy_rate']:.1%}")
        print(f"Average Confidence: {performance['metrics']['average_confidence']:.2f}")
        print(f"Average Reward: {performance['metrics']['average_reward']:+.3f}")
        
        # Stop live environment
        live_env.stop()
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error in live trading simulation: {e}{Style.RESET_ALL}")

async def run_complete_demo():
    """Run the complete visualization and portfolio demo"""
    print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🚀 COMPLETE TRADING VISUALIZATION DEMO{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}This demo showcases:{Style.RESET_ALL}")
    print("✅ Real stock price visualization with predictions")
    print("✅ Portfolio management with ₹1000 budget")
    print("✅ P&L tracking and performance metrics")
    print("✅ Live trading simulation")
    print("✅ Interactive charts and dashboards")
    print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
    
    try:
        # Run all demo components
        await demo_portfolio_system()
        await demo_visualization_system()
        await demo_live_trading_simulation()
        
        # Final summary
        print(f"\n{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🎉 DEMO COMPLETED SUCCESSFULLY!{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        
        print(f"\n{Fore.CYAN}📊 Files Created:{Style.RESET_ALL}")
        print("📁 portfolio/ - Portfolio database and trade records")
        print("📁 visualization/charts/ - Interactive charts and dashboards")
        print("📁 memory/ - AI learning experiences and patterns")
        print("📁 reports/ - Comprehensive analysis reports")
        
        print(f"\n{Fore.CYAN}🎯 Key Features Demonstrated:{Style.RESET_ALL}")
        print("✅ Real-time stock price tracking")
        print("✅ AI prediction visualization")
        print("✅ Portfolio management with ₹1000 budget")
        print("✅ Automatic trade execution")
        print("✅ P&L tracking and performance analysis")
        print("✅ Interactive charts and dashboards")
        print("✅ Memory-enhanced decision making")
        
        print(f"\n{Fore.YELLOW}💡 The AI agent now has:{Style.RESET_ALL}")
        print("🧠 Persistent memory that learns from every trade")
        print("💰 Real portfolio management with budget tracking")
        print("📊 Beautiful visualizations of performance")
        print("🎯 Live prediction and execution capabilities")
        print("📈 Comprehensive P&L and performance metrics")
        
        # Show final portfolio state
        final_summary = portfolio_manager.get_portfolio_summary()
        print(f"\n{Fore.GREEN}💰 FINAL PORTFOLIO STATE:{Style.RESET_ALL}")
        print(f"💵 Total Value: ₹{final_summary['total_value']:,.2f}")
        print(f"📈 Total Return: {final_summary['total_return_pct']:+.2f}%")
        print(f"🎯 Total Trades: {final_summary['total_trades']}")
        print(f"🏆 Win Rate: {final_summary['win_rate']:.1f}%")
        
        print(f"\n{Fore.GREEN}Ready to start live trading with visualization!")
        print(f"Run: python start_memory_agent.py{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"\n{Fore.RED}❌ Demo error: {e}{Style.RESET_ALL}")
    finally:
        # Cleanup
        fetcher = get_market_data_fetcher()
        if fetcher:
            await fetcher.close()

if __name__ == "__main__":
    # Setup basic logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the complete demo
    asyncio.run(run_complete_demo())
