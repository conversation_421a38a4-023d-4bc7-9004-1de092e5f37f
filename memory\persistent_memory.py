"""
Persistent Memory System for Trading AI Agent
Records and maintains all learning experiences, patterns, and knowledge
"""

import json
import sqlite3
import pickle
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging
import os
from pathlib import Path
import re
from textblob import TextBlob
from collections import Counter

@dataclass
class LearningExperience:
    """Single learning experience record"""
    id: str
    timestamp: datetime
    symbol: str
    sector: str
    market_conditions: Dict[str, Any]
    prediction_made: Dict[str, Any]
    actual_outcome: Dict[str, Any]
    accuracy_score: float
    reward: float
    lesson_learned: str
    confidence_before: float
    confidence_after: float

@dataclass
class MarketPattern:
    """Discovered market pattern"""
    pattern_id: str
    pattern_type: str  # "technical", "sector", "timing", "volatility"
    description: str
    conditions: Dict[str, Any]
    success_rate: float
    confidence: float
    examples: List[str]  # Experience IDs that support this pattern
    discovered_date: datetime
    last_updated: datetime

@dataclass
class KnowledgeUpdate:
    """Knowledge base update record"""
    update_id: str
    timestamp: datetime
    update_type: str  # "new_pattern", "pattern_refinement", "strategy_adjustment"
    description: str
    before_state: Dict[str, Any]
    after_state: Dict[str, Any]
    impact_score: float

class PersistentMemory:
    """
    Persistent memory system that records all AI learning
    """
    
    def __init__(self, memory_dir: str = "memory"):
        self.memory_dir = Path(memory_dir)
        self.memory_dir.mkdir(exist_ok=True)
        
        self.logger = logging.getLogger(__name__)
        
        # Database for structured data
        self.db_path = self.memory_dir / "agent_memory.db"
        
        # Files for different types of memory
        self.experiences_file = self.memory_dir / "experiences.jsonl"
        self.patterns_file = self.memory_dir / "patterns.json"
        self.knowledge_file = self.memory_dir / "knowledge_base.json"
        self.model_states_dir = self.memory_dir / "model_states"
        self.model_states_dir.mkdir(exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Load existing memory
        self.experiences: List[LearningExperience] = []
        self.patterns: Dict[str, MarketPattern] = {}
        self.knowledge_base: Dict[str, Any] = {}
        
        self._load_memory()
        
        self.logger.info(f"🧠 Persistent Memory initialized with {len(self.experiences)} experiences")
    
    def _init_database(self):
        """Initialize SQLite database for fast queries"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Experiences table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS experiences (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT,
                    symbol TEXT,
                    sector TEXT,
                    accuracy_score REAL,
                    reward REAL,
                    confidence_before REAL,
                    confidence_after REAL,
                    market_volatility REAL,
                    market_trend TEXT,
                    prediction_action TEXT,
                    actual_direction TEXT,
                    lesson_learned TEXT
                )
            ''')
            
            # Patterns table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS patterns (
                    pattern_id TEXT PRIMARY KEY,
                    pattern_type TEXT,
                    description TEXT,
                    success_rate REAL,
                    confidence REAL,
                    discovered_date TEXT,
                    last_updated TEXT,
                    example_count INTEGER
                )
            ''')
            
            # Knowledge updates table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS knowledge_updates (
                    update_id TEXT PRIMARY KEY,
                    timestamp TEXT,
                    update_type TEXT,
                    description TEXT,
                    impact_score REAL
                )
            ''')
            
            # Performance tracking table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_history (
                    timestamp TEXT PRIMARY KEY,
                    total_experiences INTEGER,
                    overall_accuracy REAL,
                    avg_confidence REAL,
                    learning_velocity REAL,
                    best_sector TEXT,
                    worst_sector TEXT,
                    total_patterns INTEGER
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
    
    def _load_memory(self):
        """Load existing memory from files"""
        try:
            # Load experiences
            if self.experiences_file.exists():
                with open(self.experiences_file, 'r') as f:
                    for line in f:
                        if line.strip():
                            exp_data = json.loads(line)
                            exp_data['timestamp'] = datetime.fromisoformat(exp_data['timestamp'])
                            experience = LearningExperience(**exp_data)
                            self.experiences.append(experience)
            
            # Load patterns
            if self.patterns_file.exists():
                with open(self.patterns_file, 'r') as f:
                    patterns_data = json.load(f)
                    for pattern_id, pattern_data in patterns_data.items():
                        pattern_data['discovered_date'] = datetime.fromisoformat(pattern_data['discovered_date'])
                        pattern_data['last_updated'] = datetime.fromisoformat(pattern_data['last_updated'])
                        self.patterns[pattern_id] = MarketPattern(**pattern_data)
            
            # Load knowledge base
            if self.knowledge_file.exists():
                with open(self.knowledge_file, 'r') as f:
                    self.knowledge_base = json.load(f)
            else:
                self._initialize_knowledge_base()
            
        except Exception as e:
            self.logger.error(f"Error loading memory: {e}")
            self._initialize_knowledge_base()
    
    def _initialize_knowledge_base(self):
        """Initialize empty knowledge base"""
        self.knowledge_base = {
            "sector_preferences": {},
            "time_patterns": {},
            "volatility_strategies": {},
            "confidence_calibration": {},
            "risk_management": {},
            "learning_insights": {},
            "model_evolution": [],
            "performance_milestones": []
        }
    
    def record_experience(self, experience: LearningExperience):
        """Record a new learning experience with NLP enhancement"""
        try:
            # Enhance lesson with NLP analysis
            experience = self._enhance_experience_with_nlp(experience)

            # Add to memory
            self.experiences.append(experience)

            # Save to file (append mode)
            with open(self.experiences_file, 'a') as f:
                exp_dict = asdict(experience)
                exp_dict['timestamp'] = experience.timestamp.isoformat()
                f.write(json.dumps(exp_dict) + '\n')

            # Save to database
            self._save_experience_to_db(experience)

            # Analyze for new patterns
            self._analyze_for_patterns(experience)

            # Update knowledge base with NLP insights
            self._update_knowledge_base_with_nlp(experience)

            # Generate semantic insights
            self._generate_semantic_insights(experience)

            self.logger.info(f"📝 Recorded experience: {experience.symbol} - {experience.lesson_learned}")

        except Exception as e:
            self.logger.error(f"Error recording experience: {e}")

    def _enhance_experience_with_nlp(self, experience: LearningExperience) -> LearningExperience:
        """Enhance experience with NLP-generated insights"""
        try:
            # Analyze the lesson learned with NLP
            lesson_blob = TextBlob(experience.lesson_learned)

            # Extract sentiment
            sentiment = lesson_blob.sentiment

            # Generate enhanced lesson with context
            enhanced_lesson = self._generate_enhanced_lesson(experience, sentiment)

            # Update the experience
            experience.lesson_learned = enhanced_lesson

            return experience

        except Exception as e:
            self.logger.error(f"Error enhancing experience with NLP: {e}")
            return experience

    def _generate_enhanced_lesson(self, experience: LearningExperience, sentiment) -> str:
        """Generate enhanced lesson using NLP and context"""
        try:
            # Base lesson
            base_lesson = experience.lesson_learned

            # Add context-aware insights
            context_insights = []

            # Accuracy-based insights
            if experience.accuracy_score > 0.8:
                context_insights.append("HIGH_ACCURACY: Strong prediction capability demonstrated")
            elif experience.accuracy_score < 0.3:
                context_insights.append("LOW_ACCURACY: Need to reassess prediction methodology")

            # Confidence calibration insights
            confidence_diff = abs(experience.confidence_before - experience.accuracy_score)
            if confidence_diff > 0.3:
                if experience.confidence_before > experience.accuracy_score:
                    context_insights.append("OVERCONFIDENCE: Reduce confidence in similar conditions")
                else:
                    context_insights.append("UNDERCONFIDENCE: Can be more confident in similar scenarios")

            # Sector-specific insights
            sector_performance = self._get_sector_performance(experience.sector)
            if sector_performance:
                if sector_performance['avg_accuracy'] > 0.7:
                    context_insights.append(f"SECTOR_STRENGTH: {experience.sector} is a strong performing sector")
                elif sector_performance['avg_accuracy'] < 0.4:
                    context_insights.append(f"SECTOR_WEAKNESS: {experience.sector} requires careful analysis")

            # Market condition insights
            volatility = experience.market_conditions.get('volatility', 0.5)
            if volatility > 0.7:
                context_insights.append("HIGH_VOLATILITY: Increased uncertainty in predictions")
            elif volatility < 0.3:
                context_insights.append("LOW_VOLATILITY: More predictable market conditions")

            # Sentiment-based insights
            if sentiment.polarity > 0.1:
                context_insights.append("POSITIVE_OUTCOME: Successful prediction strategy")
            elif sentiment.polarity < -0.1:
                context_insights.append("NEGATIVE_OUTCOME: Strategy needs adjustment")

            # Combine insights
            enhanced_lesson = base_lesson
            if context_insights:
                enhanced_lesson += " | INSIGHTS: " + " | ".join(context_insights)

            # Add learning keywords for better searchability
            keywords = self._extract_learning_keywords(experience)
            if keywords:
                enhanced_lesson += f" | KEYWORDS: {', '.join(keywords)}"

            return enhanced_lesson

        except Exception as e:
            self.logger.error(f"Error generating enhanced lesson: {e}")
            return experience.lesson_learned

    def _extract_learning_keywords(self, experience: LearningExperience) -> List[str]:
        """Extract key learning terms using NLP"""
        try:
            keywords = []

            # Add symbol and sector
            keywords.extend([experience.symbol.replace('.NS', ''), experience.sector.lower()])

            # Add accuracy category
            if experience.accuracy_score > 0.8:
                keywords.append('high_accuracy')
            elif experience.accuracy_score < 0.3:
                keywords.append('low_accuracy')
            else:
                keywords.append('medium_accuracy')

            # Add market conditions
            volatility = experience.market_conditions.get('volatility', 0.5)
            if volatility > 0.7:
                keywords.append('high_volatility')
            elif volatility < 0.3:
                keywords.append('low_volatility')

            trend = experience.market_conditions.get('trend', 'UNKNOWN')
            if trend != 'UNKNOWN':
                keywords.append(f'trend_{trend.lower()}')

            # Add prediction action
            action = experience.prediction_made.get('action', 'HOLD')
            keywords.append(f'action_{action.lower()}')

            # Add time-based keywords
            hour = experience.timestamp.hour
            if 9 <= hour <= 11:
                keywords.append('morning_session')
            elif 11 <= hour <= 14:
                keywords.append('midday_session')
            elif 14 <= hour <= 16:
                keywords.append('closing_session')

            # Add day of week
            day_names = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
            keywords.append(day_names[experience.timestamp.weekday()])

            return keywords

        except Exception as e:
            self.logger.error(f"Error extracting keywords: {e}")
            return []

    def _get_sector_performance(self, sector: str) -> Optional[Dict[str, float]]:
        """Get historical performance for a sector"""
        try:
            if sector in self.knowledge_base.get("sector_preferences", {}):
                return self.knowledge_base["sector_preferences"][sector]
            return None
        except Exception:
            return None

    def _update_knowledge_base_with_nlp(self, experience: LearningExperience):
        """Update knowledge base with NLP-enhanced insights"""
        try:
            # Call original update method
            self._update_knowledge_base(experience)

            # Add NLP-specific insights
            if "nlp_insights" not in self.knowledge_base:
                self.knowledge_base["nlp_insights"] = {
                    "common_keywords": {},
                    "sentiment_patterns": {},
                    "learning_themes": {},
                    "success_factors": [],
                    "failure_factors": []
                }

            nlp_data = self.knowledge_base["nlp_insights"]

            # Update keyword frequency
            keywords = self._extract_learning_keywords(experience)
            for keyword in keywords:
                nlp_data["common_keywords"][keyword] = nlp_data["common_keywords"].get(keyword, 0) + 1

            # Update sentiment patterns
            lesson_sentiment = TextBlob(experience.lesson_learned).sentiment
            sentiment_key = "positive" if lesson_sentiment.polarity > 0 else "negative" if lesson_sentiment.polarity < 0 else "neutral"

            if sentiment_key not in nlp_data["sentiment_patterns"]:
                nlp_data["sentiment_patterns"][sentiment_key] = {"count": 0, "avg_accuracy": 0}

            sentiment_data = nlp_data["sentiment_patterns"][sentiment_key]
            count = sentiment_data["count"]
            sentiment_data["avg_accuracy"] = (sentiment_data["avg_accuracy"] * count + experience.accuracy_score) / (count + 1)
            sentiment_data["count"] = count + 1

            # Identify success/failure factors
            if experience.accuracy_score > 0.8:
                success_factor = f"{experience.sector}_{experience.market_conditions.get('trend', 'unknown')}"
                if success_factor not in nlp_data["success_factors"]:
                    nlp_data["success_factors"].append(success_factor)
                    nlp_data["success_factors"] = nlp_data["success_factors"][-20:]  # Keep recent

            elif experience.accuracy_score < 0.3:
                failure_factor = f"{experience.sector}_{experience.market_conditions.get('volatility', 0.5):.1f}_vol"
                if failure_factor not in nlp_data["failure_factors"]:
                    nlp_data["failure_factors"].append(failure_factor)
                    nlp_data["failure_factors"] = nlp_data["failure_factors"][-20:]  # Keep recent

            # Save updated knowledge base
            self._save_knowledge_base()

        except Exception as e:
            self.logger.error(f"Error updating knowledge base with NLP: {e}")

    def _generate_semantic_insights(self, experience: LearningExperience):
        """Generate semantic insights from the experience"""
        try:
            # Analyze similar experiences using semantic similarity
            similar_experiences = self._find_semantically_similar_experiences(experience)

            if len(similar_experiences) >= 3:
                # Generate insight about similar patterns
                avg_accuracy = np.mean([exp.accuracy_score for exp in similar_experiences])

                insight = {
                    "type": "semantic_pattern",
                    "description": f"Similar trading scenarios show {avg_accuracy:.1%} average accuracy",
                    "sample_size": len(similar_experiences),
                    "confidence": min(len(similar_experiences) / 10, 1.0),
                    "timestamp": datetime.now().isoformat()
                }

                # Add to learning insights
                if "semantic_insights" not in self.knowledge_base:
                    self.knowledge_base["semantic_insights"] = []

                self.knowledge_base["semantic_insights"].append(insight)
                self.knowledge_base["semantic_insights"] = self.knowledge_base["semantic_insights"][-50:]  # Keep recent

                self._save_knowledge_base()

        except Exception as e:
            self.logger.error(f"Error generating semantic insights: {e}")

    def _find_semantically_similar_experiences(self, target_experience: LearningExperience) -> List[LearningExperience]:
        """Find experiences with similar semantic content"""
        try:
            target_keywords = set(self._extract_learning_keywords(target_experience))
            similar_experiences = []

            for exp in self.experiences[-100:]:  # Check recent experiences
                if exp.id == target_experience.id:
                    continue

                exp_keywords = set(self._extract_learning_keywords(exp))

                # Calculate semantic similarity based on keyword overlap
                overlap = len(target_keywords.intersection(exp_keywords))
                total_keywords = len(target_keywords.union(exp_keywords))

                if total_keywords > 0:
                    similarity = overlap / total_keywords

                    # Consider experiences with >50% keyword similarity
                    if similarity > 0.5:
                        similar_experiences.append(exp)

            return similar_experiences

        except Exception as e:
            self.logger.error(f"Error finding similar experiences: {e}")
            return []
    
    def _save_experience_to_db(self, experience: LearningExperience):
        """Save experience to database for fast queries"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Extract key fields for database
            market_volatility = experience.market_conditions.get('volatility', 0.0)
            market_trend = experience.market_conditions.get('trend', 'UNKNOWN')
            prediction_action = experience.prediction_made.get('action', 'HOLD')
            actual_direction = experience.actual_outcome.get('direction', 'NEUTRAL')
            
            cursor.execute('''
                INSERT OR REPLACE INTO experiences VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                experience.id,
                experience.timestamp.isoformat(),
                experience.symbol,
                experience.sector,
                experience.accuracy_score,
                experience.reward,
                experience.confidence_before,
                experience.confidence_after,
                market_volatility,
                market_trend,
                prediction_action,
                actual_direction,
                experience.lesson_learned
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error saving experience to database: {e}")
    
    def _analyze_for_patterns(self, new_experience: LearningExperience):
        """Analyze new experience for emerging patterns"""
        try:
            # Look for sector patterns
            self._check_sector_patterns(new_experience)
            
            # Look for timing patterns
            self._check_timing_patterns(new_experience)
            
            # Look for volatility patterns
            self._check_volatility_patterns(new_experience)
            
            # Look for technical patterns
            self._check_technical_patterns(new_experience)
            
        except Exception as e:
            self.logger.error(f"Error analyzing patterns: {e}")
    
    def _check_sector_patterns(self, experience: LearningExperience):
        """Check for sector-specific patterns"""
        sector = experience.sector
        
        # Get recent experiences in this sector
        sector_experiences = [exp for exp in self.experiences[-50:] 
                            if exp.sector == sector]
        
        if len(sector_experiences) >= 5:
            # Calculate sector performance
            avg_accuracy = np.mean([exp.accuracy_score for exp in sector_experiences])
            avg_reward = np.mean([exp.reward for exp in sector_experiences])
            
            # Check if this is a strong pattern
            if avg_accuracy > 0.7 or avg_accuracy < 0.3:
                pattern_id = f"sector_{sector}_{datetime.now().strftime('%Y%m')}"
                
                if pattern_id not in self.patterns:
                    pattern = MarketPattern(
                        pattern_id=pattern_id,
                        pattern_type="sector",
                        description=f"Sector {sector} shows {'strong' if avg_accuracy > 0.7 else 'weak'} performance",
                        conditions={"sector": sector, "min_experiences": 5},
                        success_rate=avg_accuracy,
                        confidence=min(len(sector_experiences) / 20, 1.0),
                        examples=[exp.id for exp in sector_experiences[-5:]],
                        discovered_date=datetime.now(),
                        last_updated=datetime.now()
                    )
                    
                    self.patterns[pattern_id] = pattern
                    self._save_patterns()
                    
                    self.logger.info(f"🔍 Discovered sector pattern: {pattern.description}")
    
    def _check_timing_patterns(self, experience: LearningExperience):
        """Check for time-based patterns"""
        hour = experience.timestamp.hour
        day_of_week = experience.timestamp.weekday()
        
        # Get experiences from similar times
        time_experiences = [exp for exp in self.experiences[-100:] 
                          if abs(exp.timestamp.hour - hour) <= 1]
        
        if len(time_experiences) >= 10:
            avg_accuracy = np.mean([exp.accuracy_score for exp in time_experiences])
            
            if avg_accuracy > 0.75:  # Strong time pattern
                pattern_id = f"timing_hour_{hour}"
                
                if pattern_id not in self.patterns:
                    pattern = MarketPattern(
                        pattern_id=pattern_id,
                        pattern_type="timing",
                        description=f"Hour {hour}:00 shows high accuracy ({avg_accuracy:.1%})",
                        conditions={"hour_range": [hour-1, hour+1]},
                        success_rate=avg_accuracy,
                        confidence=min(len(time_experiences) / 30, 1.0),
                        examples=[exp.id for exp in time_experiences[-5:]],
                        discovered_date=datetime.now(),
                        last_updated=datetime.now()
                    )
                    
                    self.patterns[pattern_id] = pattern
                    self._save_patterns()
                    
                    self.logger.info(f"⏰ Discovered timing pattern: {pattern.description}")
    
    def _check_volatility_patterns(self, experience: LearningExperience):
        """Check for volatility-based patterns"""
        volatility = experience.market_conditions.get('volatility', 0.5)
        
        # Categorize volatility
        if volatility < 0.3:
            vol_category = "low"
        elif volatility > 0.7:
            vol_category = "high"
        else:
            vol_category = "medium"
        
        # Get experiences with similar volatility
        vol_experiences = [exp for exp in self.experiences[-50:] 
                          if self._get_volatility_category(exp) == vol_category]
        
        if len(vol_experiences) >= 8:
            avg_accuracy = np.mean([exp.accuracy_score for exp in vol_experiences])
            
            pattern_id = f"volatility_{vol_category}"
            
            if pattern_id in self.patterns:
                # Update existing pattern
                pattern = self.patterns[pattern_id]
                pattern.success_rate = avg_accuracy
                pattern.confidence = min(len(vol_experiences) / 20, 1.0)
                pattern.last_updated = datetime.now()
                pattern.examples = [exp.id for exp in vol_experiences[-5:]]
            else:
                # Create new pattern
                pattern = MarketPattern(
                    pattern_id=pattern_id,
                    pattern_type="volatility",
                    description=f"{vol_category.title()} volatility environment (accuracy: {avg_accuracy:.1%})",
                    conditions={"volatility_category": vol_category},
                    success_rate=avg_accuracy,
                    confidence=min(len(vol_experiences) / 20, 1.0),
                    examples=[exp.id for exp in vol_experiences[-5:]],
                    discovered_date=datetime.now(),
                    last_updated=datetime.now()
                )
                
                self.patterns[pattern_id] = pattern
                self.logger.info(f"📊 Discovered volatility pattern: {pattern.description}")
            
            self._save_patterns()
    
    def _check_technical_patterns(self, experience: LearningExperience):
        """Check for technical analysis patterns"""
        # This would analyze technical indicators for patterns
        # Implementation depends on the technical data available
        pass
    
    def _get_volatility_category(self, experience: LearningExperience) -> str:
        """Get volatility category for an experience"""
        volatility = experience.market_conditions.get('volatility', 0.5)
        if volatility < 0.3:
            return "low"
        elif volatility > 0.7:
            return "high"
        else:
            return "medium"
    
    def _update_knowledge_base(self, experience: LearningExperience):
        """Update the knowledge base with new insights"""
        try:
            # Update sector preferences
            sector = experience.sector
            if sector not in self.knowledge_base["sector_preferences"]:
                self.knowledge_base["sector_preferences"][sector] = {
                    "total_experiences": 0,
                    "avg_accuracy": 0.0,
                    "avg_reward": 0.0,
                    "best_conditions": {},
                    "lessons": []
                }
            
            sector_data = self.knowledge_base["sector_preferences"][sector]
            
            # Update running averages
            total = sector_data["total_experiences"]
            sector_data["avg_accuracy"] = (sector_data["avg_accuracy"] * total + experience.accuracy_score) / (total + 1)
            sector_data["avg_reward"] = (sector_data["avg_reward"] * total + experience.reward) / (total + 1)
            sector_data["total_experiences"] = total + 1
            
            # Add lesson if significant
            if experience.accuracy_score > 0.8 or experience.accuracy_score < 0.2:
                sector_data["lessons"].append({
                    "timestamp": experience.timestamp.isoformat(),
                    "lesson": experience.lesson_learned,
                    "accuracy": experience.accuracy_score
                })
                
                # Keep only recent lessons
                sector_data["lessons"] = sector_data["lessons"][-10:]
            
            # Update confidence calibration
            confidence_diff = abs(experience.confidence_before - experience.accuracy_score)
            if "confidence_calibration" not in self.knowledge_base:
                self.knowledge_base["confidence_calibration"] = {"total_diff": 0, "count": 0}
            
            cal_data = self.knowledge_base["confidence_calibration"]
            cal_data["total_diff"] = (cal_data["total_diff"] * cal_data["count"] + confidence_diff) / (cal_data["count"] + 1)
            cal_data["count"] += 1
            
            # Save updated knowledge base
            self._save_knowledge_base()
            
        except Exception as e:
            self.logger.error(f"Error updating knowledge base: {e}")
    
    def _save_patterns(self):
        """Save patterns to file"""
        try:
            patterns_data = {}
            for pattern_id, pattern in self.patterns.items():
                pattern_dict = asdict(pattern)
                pattern_dict['discovered_date'] = pattern.discovered_date.isoformat()
                pattern_dict['last_updated'] = pattern.last_updated.isoformat()
                patterns_data[pattern_id] = pattern_dict
            
            with open(self.patterns_file, 'w') as f:
                json.dump(patterns_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving patterns: {e}")
    
    def _save_knowledge_base(self):
        """Save knowledge base to file"""
        try:
            with open(self.knowledge_file, 'w') as f:
                json.dump(self.knowledge_base, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving knowledge base: {e}")
    
    def get_relevant_patterns(self, context: Dict[str, Any]) -> List[MarketPattern]:
        """Get patterns relevant to current context"""
        relevant_patterns = []
        
        for pattern in self.patterns.values():
            if self._pattern_matches_context(pattern, context):
                relevant_patterns.append(pattern)
        
        # Sort by confidence and success rate
        relevant_patterns.sort(key=lambda p: p.confidence * p.success_rate, reverse=True)
        
        return relevant_patterns
    
    def _pattern_matches_context(self, pattern: MarketPattern, context: Dict[str, Any]) -> bool:
        """Check if a pattern matches the current context"""
        try:
            conditions = pattern.conditions
            
            # Check sector match
            if "sector" in conditions and context.get("sector") != conditions["sector"]:
                return False
            
            # Check time match
            if "hour_range" in conditions:
                current_hour = context.get("hour", datetime.now().hour)
                hour_range = conditions["hour_range"]
                if not (hour_range[0] <= current_hour <= hour_range[1]):
                    return False
            
            # Check volatility match
            if "volatility_category" in conditions:
                current_vol = context.get("volatility", 0.5)
                current_category = "low" if current_vol < 0.3 else "high" if current_vol > 0.7 else "medium"
                if current_category != conditions["volatility_category"]:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error matching pattern: {e}")
            return False
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Get comprehensive memory summary"""
        try:
            total_experiences = len(self.experiences)
            
            if total_experiences == 0:
                return {"status": "empty", "message": "No experiences recorded yet"}
            
            # Calculate overall statistics
            recent_experiences = self.experiences[-50:] if total_experiences > 50 else self.experiences
            
            overall_accuracy = np.mean([exp.accuracy_score for exp in recent_experiences])
            overall_reward = np.mean([exp.reward for exp in recent_experiences])
            
            # Sector breakdown
            sector_stats = {}
            for exp in recent_experiences:
                if exp.sector not in sector_stats:
                    sector_stats[exp.sector] = {"count": 0, "accuracy": 0, "reward": 0}
                
                sector_stats[exp.sector]["count"] += 1
                sector_stats[exp.sector]["accuracy"] += exp.accuracy_score
                sector_stats[exp.sector]["reward"] += exp.reward
            
            # Calculate averages
            for sector in sector_stats:
                count = sector_stats[sector]["count"]
                sector_stats[sector]["accuracy"] /= count
                sector_stats[sector]["reward"] /= count
            
            return {
                "total_experiences": total_experiences,
                "total_patterns": len(self.patterns),
                "overall_accuracy": overall_accuracy,
                "overall_reward": overall_reward,
                "sector_performance": sector_stats,
                "recent_patterns": list(self.patterns.keys())[-5:],
                "knowledge_areas": list(self.knowledge_base.keys()),
                "memory_size_mb": self._calculate_memory_size()
            }
            
        except Exception as e:
            self.logger.error(f"Error generating memory summary: {e}")
            return {"status": "error", "message": str(e)}
    
    def _calculate_memory_size(self) -> float:
        """Calculate total memory size in MB"""
        try:
            total_size = 0
            for file_path in [self.experiences_file, self.patterns_file, self.knowledge_file, self.db_path]:
                if file_path.exists():
                    total_size += file_path.stat().st_size
            
            return total_size / (1024 * 1024)  # Convert to MB
            
        except Exception:
            return 0.0

# Global memory instance
persistent_memory = PersistentMemory()
