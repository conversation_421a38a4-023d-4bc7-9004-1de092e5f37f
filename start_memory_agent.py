#!/usr/bin/env python3
"""
Memory-Enhanced Trading AI Agent Launcher
Demonstrates the complete system with persistent memory and NLP insights
"""

import asyncio
import sys
import subprocess
import os
from colorama import init, Fore, Style
from datetime import datetime

# Initialize colorama
init()

def print_banner():
    """Print the startup banner"""
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🧠 MEMORY-ENHANCED TRADING AI AGENT{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Persistent Memory | NLP Insights | Continuous Learning{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")

def show_memory_features():
    """Show memory system features"""
    print(f"\n{Fore.CYAN}🧠 MEMORY SYSTEM FEATURES:{Style.RESET_ALL}")
    print(f"✅ {Fore.GREEN}Persistent Experience Recording{Style.RESET_ALL} - Every trade saved forever")
    print(f"✅ {Fore.GREEN}NLP-Enhanced Learning{Style.RESET_ALL} - Intelligent lesson extraction")
    print(f"✅ {Fore.GREEN}Pattern Discovery{Style.RESET_ALL} - Automatic pattern recognition")
    print(f"✅ {Fore.GREEN}Memory-Based Decisions{Style.RESET_ALL} - Predictions use historical knowledge")
    print(f"✅ {Fore.GREEN}Sector Intelligence{Style.RESET_ALL} - Learns sector-specific strategies")
    print(f"✅ {Fore.GREEN}Confidence Calibration{Style.RESET_ALL} - Improves confidence accuracy")
    print(f"✅ {Fore.GREEN}AI Recommendations{Style.RESET_ALL} - Actionable insights from memory")
    print(f"✅ {Fore.GREEN}Comprehensive Reports{Style.RESET_ALL} - Detailed memory analysis")

def show_menu():
    """Show the main menu"""
    print(f"\n{Fore.CYAN}📋 CHOOSE YOUR OPTION:{Style.RESET_ALL}")
    print(f"1. 🧠 Demo Memory System (See how memory works)")
    print(f"2. 🚀 Start Memory-Enhanced Trading Agent")
    print(f"3. 📊 View Memory Report")
    print(f"4. 🔍 Analyze Memory Patterns")
    print(f"5. 🏭 Memory-Enhanced Sector Trading")
    print(f"6. 📈 Show Learning Progress")
    print(f"7. 💡 Get AI Recommendations")
    print(f"8. 🧹 Clear Memory (Start Fresh)")
    print(f"9. ❓ Help & Documentation")
    print(f"10. 🚪 Exit")

def run_memory_demo():
    """Run the memory system demo"""
    print(f"\n{Fore.YELLOW}🧠 Starting Memory System Demo...{Style.RESET_ALL}")
    try:
        subprocess.run([sys.executable, "demo_memory.py"], check=True)
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Memory demo failed to run{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Demo stopped by user{Style.RESET_ALL}")

def start_memory_enhanced_agent():
    """Start the memory-enhanced trading agent"""
    print(f"\n{Fore.YELLOW}🚀 Starting Memory-Enhanced Trading Agent...{Style.RESET_ALL}")
    print(f"{Fore.GREEN}This agent will:{Style.RESET_ALL}")
    print("• Make predictions using accumulated memory")
    print("• Record every experience with NLP insights")
    print("• Discover patterns automatically")
    print("• Improve decision-making over time")
    print("• Generate actionable recommendations")
    
    print(f"\n{Fore.CYAN}Press Ctrl+C to stop the agent{Style.RESET_ALL}\n")
    
    try:
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Agent failed to start{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Agent stopped by user{Style.RESET_ALL}")

def view_memory_report():
    """View the latest memory report"""
    print(f"\n{Fore.YELLOW}📊 Generating Memory Report...{Style.RESET_ALL}")
    
    try:
        # Import memory components
        from memory.memory_visualizer import memory_visualizer
        from memory.persistent_memory import persistent_memory
        
        # Generate report
        report = memory_visualizer.generate_memory_report()
        
        # Display key insights
        print(f"\n{Fore.GREEN}📈 MEMORY INSIGHTS{Style.RESET_ALL}")
        
        summary = report.get('summary', {})
        print(f"Total Experiences: {summary.get('total_experiences', 0)}")
        print(f"Overall Accuracy: {summary.get('overall_accuracy', 0):.1%}")
        print(f"Memory Size: {summary.get('memory_size_mb', 0):.2f} MB")
        
        # Sector performance
        sector_perf = summary.get('sector_performance', {})
        if sector_perf:
            print(f"\n{Fore.BLUE}🏭 SECTOR PERFORMANCE{Style.RESET_ALL}")
            sorted_sectors = sorted(sector_perf.items(), key=lambda x: x[1]['accuracy'], reverse=True)
            for sector, perf in sorted_sectors:
                print(f"{sector}: {perf['accuracy']:.1%} accuracy ({perf['count']} trades)")
        
        # NLP insights
        nlp_insights = report.get('nlp_insights', {})
        if nlp_insights:
            print(f"\n{Fore.MAGENTA}🤖 NLP INSIGHTS{Style.RESET_ALL}")
            
            keywords = nlp_insights.get('common_keywords', {})
            if keywords:
                top_keywords = sorted(keywords.items(), key=lambda x: x[1], reverse=True)[:5]
                print(f"Top Keywords: {', '.join([f'{k}({v})' for k, v in top_keywords])}")
            
            success_factors = nlp_insights.get('success_factors', [])
            if success_factors:
                print(f"Success Factors: {', '.join(success_factors[-3:])}")
        
        # Recommendations
        recommendations = report.get('recommendations', [])
        if recommendations:
            print(f"\n{Fore.YELLOW}💡 AI RECOMMENDATIONS{Style.RESET_ALL}")
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec}")
        
        # Save report
        report_file = memory_visualizer.save_memory_report()
        if report_file:
            print(f"\n💾 Full report saved to: {report_file}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error generating report: {e}{Style.RESET_ALL}")

def analyze_memory_patterns():
    """Analyze memory patterns"""
    print(f"\n{Fore.YELLOW}🔍 Analyzing Memory Patterns...{Style.RESET_ALL}")
    
    try:
        from memory.persistent_memory import persistent_memory
        
        # Get patterns
        patterns = persistent_memory.patterns
        
        if not patterns:
            print(f"{Fore.YELLOW}No patterns discovered yet. Trade more to build patterns!{Style.RESET_ALL}")
            return
        
        print(f"\n{Fore.GREEN}📊 DISCOVERED PATTERNS{Style.RESET_ALL}")
        
        for pattern_id, pattern in patterns.items():
            print(f"\n🔍 {pattern.pattern_type.upper()} Pattern:")
            print(f"   Description: {pattern.description}")
            print(f"   Success Rate: {pattern.success_rate:.1%}")
            print(f"   Confidence: {pattern.confidence:.1%}")
            print(f"   Examples: {len(pattern.examples)}")
            print(f"   Discovered: {pattern.discovered_date.strftime('%Y-%m-%d %H:%M')}")
        
        # Show pattern evolution
        pattern_types = {}
        for pattern in patterns.values():
            if pattern.pattern_type not in pattern_types:
                pattern_types[pattern.pattern_type] = []
            pattern_types[pattern.pattern_type].append(pattern)
        
        print(f"\n{Fore.BLUE}📈 PATTERN EVOLUTION{Style.RESET_ALL}")
        for pattern_type, pattern_list in pattern_types.items():
            print(f"{pattern_type.title()}: {len(pattern_list)} patterns discovered")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error analyzing patterns: {e}{Style.RESET_ALL}")

def show_learning_progress():
    """Show learning progress over time"""
    print(f"\n{Fore.YELLOW}📈 Analyzing Learning Progress...{Style.RESET_ALL}")
    
    try:
        from memory.persistent_memory import persistent_memory
        
        experiences = persistent_memory.experiences
        
        if len(experiences) < 5:
            print(f"{Fore.YELLOW}Need at least 5 experiences to show progress. Current: {len(experiences)}{Style.RESET_ALL}")
            return
        
        # Calculate progress metrics
        total = len(experiences)
        recent_count = min(10, total // 2)
        
        recent_accuracy = sum(exp.accuracy_score for exp in experiences[-recent_count:]) / recent_count
        older_accuracy = sum(exp.accuracy_score for exp in experiences[:recent_count]) / recent_count
        
        improvement = recent_accuracy - older_accuracy
        
        print(f"\n{Fore.GREEN}📊 LEARNING PROGRESS{Style.RESET_ALL}")
        print(f"Total Experiences: {total}")
        print(f"Early Accuracy: {older_accuracy:.1%}")
        print(f"Recent Accuracy: {recent_accuracy:.1%}")
        print(f"Improvement: {improvement:+.1%}")
        
        if improvement > 0.05:
            print(f"{Fore.GREEN}🎉 Excellent learning progress!{Style.RESET_ALL}")
        elif improvement > 0:
            print(f"{Fore.YELLOW}📈 Good learning progress{Style.RESET_ALL}")
        else:
            print(f"{Fore.BLUE}📊 Stable performance{Style.RESET_ALL}")
        
        # Show sector progress
        from collections import defaultdict
        sector_progress = defaultdict(list)
        
        for exp in experiences:
            sector_progress[exp.sector].append(exp.accuracy_score)
        
        print(f"\n{Fore.BLUE}🏭 SECTOR PROGRESS{Style.RESET_ALL}")
        for sector, accuracies in sector_progress.items():
            if len(accuracies) >= 3:
                avg_accuracy = sum(accuracies) / len(accuracies)
                print(f"{sector}: {avg_accuracy:.1%} avg ({len(accuracies)} trades)")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error analyzing progress: {e}{Style.RESET_ALL}")

def clear_memory():
    """Clear all memory (start fresh)"""
    print(f"\n{Fore.RED}🧹 CLEAR MEMORY{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}⚠️  This will delete all accumulated learning and start fresh!{Style.RESET_ALL}")
    
    confirm = input(f"\n{Fore.CYAN}Are you sure? Type 'YES' to confirm: {Style.RESET_ALL}")
    
    if confirm == "YES":
        try:
            import shutil
            from pathlib import Path
            
            # Remove memory directory
            memory_dir = Path("memory")
            if memory_dir.exists():
                shutil.rmtree(memory_dir)
            
            # Remove reports directory
            reports_dir = Path("reports")
            if reports_dir.exists():
                shutil.rmtree(reports_dir)
            
            print(f"{Fore.GREEN}✅ Memory cleared successfully!{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}The agent will start learning from scratch on next run.{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"{Fore.RED}❌ Error clearing memory: {e}{Style.RESET_ALL}")
    else:
        print(f"{Fore.YELLOW}Memory clearing cancelled.{Style.RESET_ALL}")

def show_help():
    """Show help and documentation"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📚 MEMORY-ENHANCED TRADING AI HELP{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.GREEN}🧠 What is the Memory System?{Style.RESET_ALL}")
    print("The memory system gives the AI agent a persistent brain that:")
    print("• Records every trading experience with detailed context")
    print("• Uses NLP to extract intelligent insights from outcomes")
    print("• Discovers patterns automatically (sector, timing, volatility)")
    print("• Makes smarter predictions based on accumulated knowledge")
    print("• Provides actionable recommendations for improvement")
    print("• Never forgets - knowledge persists across sessions")
    
    print(f"\n{Fore.GREEN}🚀 How It Works:{Style.RESET_ALL}")
    print("1. Agent makes prediction → Records experience with NLP insights")
    print("2. Memory system analyzes patterns → Discovers success/failure factors")
    print("3. Future predictions use memory → Enhanced decision making")
    print("4. Continuous learning → Performance improves over time")
    
    print(f"\n{Fore.GREEN}📁 Memory Files:{Style.RESET_ALL}")
    print("• memory/experiences.jsonl - All trading experiences")
    print("• memory/knowledge_base.json - Accumulated insights")
    print("• memory/patterns.json - Discovered patterns")
    print("• memory/agent_memory.db - SQLite database for queries")
    print("• reports/ - Comprehensive analysis reports")
    
    print(f"\n{Fore.GREEN}💡 Pro Tips:{Style.RESET_ALL}")
    print("• Let the agent trade for at least 50 experiences to see patterns")
    print("• Check memory reports regularly for insights")
    print("• The agent gets smarter with every trade")
    print("• Memory-enhanced predictions are more accurate")
    print("• Use sector-specific trading to build specialized knowledge")

def main():
    """Main function"""
    print_banner()
    show_memory_features()
    
    while True:
        show_menu()
        
        try:
            choice = input(f"\n{Fore.CYAN}Enter your choice (1-10): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                run_memory_demo()
            
            elif choice == "2":
                start_memory_enhanced_agent()
            
            elif choice == "3":
                view_memory_report()
            
            elif choice == "4":
                analyze_memory_patterns()
            
            elif choice == "5":
                print(f"\n{Fore.YELLOW}🏭 Starting Sector-Specific Memory-Enhanced Trading...{Style.RESET_ALL}")
                sector = input(f"{Fore.CYAN}Choose sector (IT/BANKING/PHARMA/AUTO/ENERGY): {Style.RESET_ALL}").upper()
                if sector in ["IT", "BANKING", "PHARMA", "AUTO", "ENERGY"]:
                    try:
                        subprocess.run([sys.executable, "main.py", "--sector", sector], check=True)
                    except (subprocess.CalledProcessError, KeyboardInterrupt):
                        print(f"\n{Fore.YELLOW}Sector trading stopped{Style.RESET_ALL}")
                else:
                    print(f"{Fore.RED}Invalid sector{Style.RESET_ALL}")
            
            elif choice == "6":
                show_learning_progress()
            
            elif choice == "7":
                view_memory_report()  # Shows recommendations
            
            elif choice == "8":
                clear_memory()
            
            elif choice == "9":
                show_help()
            
            elif choice == "10":
                print(f"\n{Fore.YELLOW}👋 Goodbye! Your AI agent's memory will persist for next time!{Style.RESET_ALL}")
                break
            
            else:
                print(f"{Fore.RED}❌ Invalid choice. Please enter 1-10.{Style.RESET_ALL}")
        
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}👋 Goodbye! Your AI agent's memory will persist for next time!{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
