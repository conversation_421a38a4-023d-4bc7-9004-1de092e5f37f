"""
Real-Time Trading Visualization System
Shows stock graphs, predictions, and P&L tracking
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.animation import FuncAnimation
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
from pathlib import Path
import seaborn as sns
from collections import deque
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

from portfolio.portfolio_manager import portfolio_manager
from data.market_data_fetcher import get_market_data_fetcher
from environment.live_prediction_env import live_env

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class TradingVisualizer:
    """
    Real-time visualization system for trading AI agent
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Data storage for visualization
        self.price_data: Dict[str, deque] = {}
        self.prediction_data: Dict[str, deque] = {}
        self.portfolio_data = deque(maxlen=1000)
        self.pnl_data = deque(maxlen=1000)
        
        # Visualization settings
        self.max_data_points = 100
        self.update_interval = 5000  # 5 seconds
        
        # Create visualization directory
        self.viz_dir = Path("visualization/charts")
        self.viz_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("📊 Trading Visualizer initialized")
    
    def create_live_dashboard(self, symbols: List[str]) -> None:
        """Create live trading dashboard with multiple charts"""
        try:
            # Create subplots
            fig = make_subplots(
                rows=3, cols=2,
                subplot_titles=[
                    'Stock Prices & Predictions', 'Portfolio Value',
                    'P&L Tracking', 'Position Distribution',
                    'Trading Activity', 'Performance Metrics'
                ],
                specs=[
                    [{"secondary_y": True}, {"type": "scatter"}],
                    [{"type": "scatter"}, {"type": "pie"}],
                    [{"type": "bar"}, {"type": "table"}]
                ]
            )
            
            # Initialize data for symbols
            for symbol in symbols:
                self.price_data[symbol] = deque(maxlen=self.max_data_points)
                self.prediction_data[symbol] = deque(maxlen=self.max_data_points)
            
            # Start real-time updates
            self._update_dashboard_data(symbols)
            
            # Create initial plots
            self._plot_stock_prices(fig, symbols, row=1, col=1)
            self._plot_portfolio_value(fig, row=1, col=2)
            self._plot_pnl_tracking(fig, row=2, col=1)
            self._plot_position_distribution(fig, row=2, col=2)
            self._plot_trading_activity(fig, row=3, col=1)
            self._plot_performance_metrics(fig, row=3, col=2)
            
            # Update layout
            fig.update_layout(
                title="🤖 AI Trading Agent - Live Dashboard",
                height=1200,
                showlegend=True,
                template="plotly_dark"
            )
            
            # Save and show
            dashboard_path = self.viz_dir / f"live_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            fig.write_html(dashboard_path)
            
            self.logger.info(f"📊 Live dashboard created: {dashboard_path}")
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Error creating live dashboard: {e}")
            return None
    
    def _update_dashboard_data(self, symbols: List[str]):
        """Update data for dashboard"""
        try:
            # Get current market data
            fetcher = get_market_data_fetcher()
            
            for symbol in symbols:
                # Get real-time price data
                market_data = fetcher.fetch_realtime_data(symbol)
                if market_data:
                    timestamp = datetime.now()
                    price = market_data.close
                    
                    self.price_data[symbol].append({
                        'timestamp': timestamp,
                        'price': price,
                        'volume': market_data.volume
                    })
                
                # Get prediction data
                predictions = self._get_recent_predictions(symbol)
                if predictions:
                    self.prediction_data[symbol].extend(predictions)
            
            # Update portfolio data
            portfolio_summary = portfolio_manager.get_portfolio_summary()
            self.portfolio_data.append({
                'timestamp': datetime.now(),
                'total_value': portfolio_summary.get('total_value', 1000),
                'cash_balance': portfolio_summary.get('cash_balance', 1000),
                'pnl': portfolio_summary.get('total_pnl', 0)
            })
            
        except Exception as e:
            self.logger.error(f"Error updating dashboard data: {e}")
    
    def _get_recent_predictions(self, symbol: str) -> List[Dict]:
        """Get recent predictions for a symbol"""
        try:
            # Get predictions from live environment
            performance = live_env.get_performance_summary()
            
            # Filter predictions for this symbol
            predictions = []
            # This would be implemented based on live_env structure
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"Error getting predictions for {symbol}: {e}")
            return []
    
    def _plot_stock_prices(self, fig, symbols: List[str], row: int, col: int):
        """Plot stock prices with prediction overlay"""
        try:
            colors = px.colors.qualitative.Set1
            
            for i, symbol in enumerate(symbols[:3]):  # Limit to 3 symbols for clarity
                if symbol in self.price_data and self.price_data[symbol]:
                    data = list(self.price_data[symbol])
                    timestamps = [d['timestamp'] for d in data]
                    prices = [d['price'] for d in data]
                    
                    # Plot actual prices
                    fig.add_trace(
                        go.Scatter(
                            x=timestamps,
                            y=prices,
                            mode='lines+markers',
                            name=f'{symbol} Price',
                            line=dict(color=colors[i % len(colors)], width=2),
                            marker=dict(size=4)
                        ),
                        row=row, col=col
                    )
                    
                    # Plot predictions if available
                    if symbol in self.prediction_data and self.prediction_data[symbol]:
                        pred_data = list(self.prediction_data[symbol])
                        pred_timestamps = [d['timestamp'] for d in pred_data]
                        pred_prices = [d['predicted_price'] for d in pred_data]
                        
                        fig.add_trace(
                            go.Scatter(
                                x=pred_timestamps,
                                y=pred_prices,
                                mode='lines',
                                name=f'{symbol} Prediction',
                                line=dict(color=colors[i % len(colors)], width=1, dash='dash'),
                                opacity=0.7
                            ),
                            row=row, col=col
                        )
            
            fig.update_xaxes(title_text="Time", row=row, col=col)
            fig.update_yaxes(title_text="Price (₹)", row=row, col=col)
            
        except Exception as e:
            self.logger.error(f"Error plotting stock prices: {e}")
    
    def _plot_portfolio_value(self, fig, row: int, col: int):
        """Plot portfolio value over time"""
        try:
            if self.portfolio_data:
                data = list(self.portfolio_data)
                timestamps = [d['timestamp'] for d in data]
                values = [d['total_value'] for d in data]
                
                fig.add_trace(
                    go.Scatter(
                        x=timestamps,
                        y=values,
                        mode='lines+markers',
                        name='Portfolio Value',
                        line=dict(color='green', width=3),
                        fill='tonexty'
                    ),
                    row=row, col=col
                )
                
                # Add initial capital line
                fig.add_hline(
                    y=1000,
                    line_dash="dash",
                    line_color="red",
                    annotation_text="Initial Capital (₹1000)",
                    row=row, col=col
                )
            
            fig.update_xaxes(title_text="Time", row=row, col=col)
            fig.update_yaxes(title_text="Value (₹)", row=row, col=col)
            
        except Exception as e:
            self.logger.error(f"Error plotting portfolio value: {e}")
    
    def _plot_pnl_tracking(self, fig, row: int, col: int):
        """Plot P&L tracking"""
        try:
            if self.portfolio_data:
                data = list(self.portfolio_data)
                timestamps = [d['timestamp'] for d in data]
                pnl_values = [d['pnl'] for d in data]
                
                # Color based on positive/negative
                colors = ['green' if pnl >= 0 else 'red' for pnl in pnl_values]
                
                fig.add_trace(
                    go.Bar(
                        x=timestamps,
                        y=pnl_values,
                        name='P&L',
                        marker_color=colors
                    ),
                    row=row, col=col
                )
            
            fig.update_xaxes(title_text="Time", row=row, col=col)
            fig.update_yaxes(title_text="P&L (₹)", row=row, col=col)
            
        except Exception as e:
            self.logger.error(f"Error plotting P&L: {e}")
    
    def _plot_position_distribution(self, fig, row: int, col: int):
        """Plot current position distribution"""
        try:
            portfolio_summary = portfolio_manager.get_portfolio_summary()
            positions = portfolio_summary.get('positions', {})
            
            if positions:
                symbols = list(positions.keys())
                values = [pos['quantity'] * pos['current_price'] for pos in positions.values()]
                
                fig.add_trace(
                    go.Pie(
                        labels=symbols,
                        values=values,
                        name="Positions"
                    ),
                    row=row, col=col
                )
            
        except Exception as e:
            self.logger.error(f"Error plotting position distribution: {e}")
    
    def _plot_trading_activity(self, fig, row: int, col: int):
        """Plot recent trading activity"""
        try:
            portfolio_summary = portfolio_manager.get_portfolio_summary()
            recent_trades = portfolio_summary.get('recent_trades', [])
            
            if recent_trades:
                buy_trades = [t for t in recent_trades if t['action'] == 'BUY']
                sell_trades = [t for t in recent_trades if t['action'] == 'SELL']
                
                fig.add_trace(
                    go.Bar(
                        x=['BUY', 'SELL'],
                        y=[len(buy_trades), len(sell_trades)],
                        name='Trade Count',
                        marker_color=['green', 'red']
                    ),
                    row=row, col=col
                )
            
            fig.update_xaxes(title_text="Trade Type", row=row, col=col)
            fig.update_yaxes(title_text="Count", row=row, col=col)
            
        except Exception as e:
            self.logger.error(f"Error plotting trading activity: {e}")
    
    def _plot_performance_metrics(self, fig, row: int, col: int):
        """Plot performance metrics table"""
        try:
            portfolio_summary = portfolio_manager.get_portfolio_summary()
            
            metrics = [
                ['Initial Capital', f"₹{portfolio_summary.get('initial_capital', 1000):,.2f}"],
                ['Current Value', f"₹{portfolio_summary.get('total_value', 1000):,.2f}"],
                ['Total P&L', f"₹{portfolio_summary.get('total_pnl', 0):+,.2f}"],
                ['Return %', f"{portfolio_summary.get('total_return_pct', 0):+.2f}%"],
                ['Total Trades', f"{portfolio_summary.get('total_trades', 0)}"],
                ['Win Rate', f"{portfolio_summary.get('win_rate', 0):.1f}%"],
                ['Cash Balance', f"₹{portfolio_summary.get('cash_balance', 1000):,.2f}"]
            ]
            
            fig.add_trace(
                go.Table(
                    header=dict(values=['Metric', 'Value']),
                    cells=dict(values=[[m[0] for m in metrics], [m[1] for m in metrics]])
                ),
                row=row, col=col
            )
            
        except Exception as e:
            self.logger.error(f"Error plotting performance metrics: {e}")
    
    def create_stock_chart_with_predictions(self, symbol: str, days: int = 7) -> go.Figure:
        """Create detailed stock chart with predictions for a single symbol"""
        try:
            # Get historical data
            fetcher = get_market_data_fetcher()
            historical_data = fetcher.get_today_data_for_training([symbol])
            
            if symbol not in historical_data or historical_data[symbol].empty:
                self.logger.warning(f"No data available for {symbol}")
                return None
            
            df = historical_data[symbol]
            
            # Create candlestick chart
            fig = go.Figure()
            
            # Add candlestick
            fig.add_trace(go.Candlestick(
                x=df.index,
                open=df['Open'],
                high=df['High'],
                low=df['Low'],
                close=df['Close'],
                name=f'{symbol} Price',
                increasing_line_color='green',
                decreasing_line_color='red'
            ))
            
            # Add volume
            fig.add_trace(go.Bar(
                x=df.index,
                y=df['Volume'],
                name='Volume',
                yaxis='y2',
                opacity=0.3
            ))
            
            # Add technical indicators if available
            if 'SMA_20' in df.columns:
                fig.add_trace(go.Scatter(
                    x=df.index,
                    y=df['SMA_20'],
                    mode='lines',
                    name='SMA 20',
                    line=dict(color='blue', width=1)
                ))
            
            if 'EMA_12' in df.columns:
                fig.add_trace(go.Scatter(
                    x=df.index,
                    y=df['EMA_12'],
                    mode='lines',
                    name='EMA 12',
                    line=dict(color='orange', width=1)
                ))
            
            # Add Bollinger Bands if available
            if 'BB_Upper' in df.columns and 'BB_Lower' in df.columns:
                fig.add_trace(go.Scatter(
                    x=df.index,
                    y=df['BB_Upper'],
                    mode='lines',
                    name='BB Upper',
                    line=dict(color='gray', width=1, dash='dash'),
                    showlegend=False
                ))
                
                fig.add_trace(go.Scatter(
                    x=df.index,
                    y=df['BB_Lower'],
                    mode='lines',
                    name='BB Lower',
                    line=dict(color='gray', width=1, dash='dash'),
                    fill='tonexty',
                    fillcolor='rgba(128,128,128,0.1)',
                    showlegend=False
                ))
            
            # Add prediction markers (if any)
            predictions = self._get_predictions_for_symbol(symbol)
            if predictions:
                pred_times = [p['timestamp'] for p in predictions]
                pred_prices = [p['predicted_price'] for p in predictions]
                pred_actions = [p['action'] for p in predictions]
                
                # Color code by action
                colors = {'BUY': 'green', 'SELL': 'red', 'HOLD': 'yellow'}
                
                for i, (time, price, action) in enumerate(zip(pred_times, pred_prices, pred_actions)):
                    fig.add_trace(go.Scatter(
                        x=[time],
                        y=[price],
                        mode='markers',
                        name=f'Prediction {action}',
                        marker=dict(
                            color=colors.get(action, 'blue'),
                            size=10,
                            symbol='diamond'
                        ),
                        showlegend=i == 0  # Only show legend for first prediction
                    ))
            
            # Update layout
            fig.update_layout(
                title=f'{symbol} - Stock Price with AI Predictions',
                xaxis_title='Time',
                yaxis_title='Price (₹)',
                yaxis2=dict(
                    title='Volume',
                    overlaying='y',
                    side='right'
                ),
                template='plotly_dark',
                height=600
            )
            
            # Save chart
            chart_path = self.viz_dir / f"{symbol}_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            fig.write_html(chart_path)
            
            self.logger.info(f"📊 Stock chart created for {symbol}: {chart_path}")
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Error creating stock chart for {symbol}: {e}")
            return None
    
    def _get_predictions_for_symbol(self, symbol: str) -> List[Dict]:
        """Get AI predictions for a specific symbol"""
        try:
            # This would integrate with the live prediction environment
            # For now, return empty list
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting predictions for {symbol}: {e}")
            return []
    
    def create_portfolio_performance_chart(self) -> go.Figure:
        """Create comprehensive portfolio performance chart"""
        try:
            portfolio_summary = portfolio_manager.get_portfolio_summary()
            
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=[
                    'Portfolio Value Over Time',
                    'P&L Distribution',
                    'Position Allocation',
                    'Trading Performance'
                ],
                specs=[
                    [{"secondary_y": True}, {"type": "histogram"}],
                    [{"type": "pie"}, {"type": "bar"}]
                ]
            )
            
            # Portfolio value over time
            if self.portfolio_data:
                data = list(self.portfolio_data)
                timestamps = [d['timestamp'] for d in data]
                values = [d['total_value'] for d in data]
                pnl = [d['pnl'] for d in data]
                
                fig.add_trace(
                    go.Scatter(
                        x=timestamps,
                        y=values,
                        mode='lines+markers',
                        name='Portfolio Value',
                        line=dict(color='blue', width=2)
                    ),
                    row=1, col=1
                )
                
                fig.add_trace(
                    go.Scatter(
                        x=timestamps,
                        y=pnl,
                        mode='lines',
                        name='P&L',
                        line=dict(color='green', width=1),
                        yaxis='y2'
                    ),
                    row=1, col=1, secondary_y=True
                )
            
            # P&L distribution
            if portfolio_summary.get('recent_trades'):
                trades = portfolio_summary['recent_trades']
                pnl_values = [t.get('amount', 0) - 100 for t in trades]  # Simplified P&L
                
                fig.add_trace(
                    go.Histogram(
                        x=pnl_values,
                        name='P&L Distribution',
                        nbinsx=20
                    ),
                    row=1, col=2
                )
            
            # Position allocation
            positions = portfolio_summary.get('positions', {})
            if positions:
                symbols = list(positions.keys())
                values = [pos['quantity'] * pos['current_price'] for pos in positions.values()]
                
                fig.add_trace(
                    go.Pie(
                        labels=symbols,
                        values=values,
                        name="Position Allocation"
                    ),
                    row=2, col=1
                )
            
            # Trading performance metrics
            metrics = {
                'Total Return': portfolio_summary.get('total_return_pct', 0),
                'Win Rate': portfolio_summary.get('win_rate', 0),
                'Total Trades': portfolio_summary.get('total_trades', 0)
            }
            
            fig.add_trace(
                go.Bar(
                    x=list(metrics.keys()),
                    y=list(metrics.values()),
                    name='Performance Metrics'
                ),
                row=2, col=2
            )
            
            # Update layout
            fig.update_layout(
                title='📊 Portfolio Performance Dashboard',
                height=800,
                template='plotly_dark'
            )
            
            # Save chart
            perf_path = self.viz_dir / f"portfolio_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            fig.write_html(perf_path)
            
            self.logger.info(f"📊 Portfolio performance chart created: {perf_path}")
            
            return fig
            
        except Exception as e:
            self.logger.error(f"Error creating portfolio performance chart: {e}")
            return None
    
    def save_daily_summary_chart(self) -> str:
        """Save daily trading summary chart"""
        try:
            portfolio_summary = portfolio_manager.get_portfolio_summary()
            
            # Create summary figure
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Daily Trading Summary - {datetime.now().strftime("%Y-%m-%d")}', fontsize=16)
            
            # Portfolio value
            if self.portfolio_data:
                data = list(self.portfolio_data)
                timestamps = [d['timestamp'] for d in data]
                values = [d['total_value'] for d in data]
                
                ax1.plot(timestamps, values, 'b-', linewidth=2, label='Portfolio Value')
                ax1.axhline(y=1000, color='r', linestyle='--', label='Initial Capital')
                ax1.set_title('Portfolio Value')
                ax1.set_ylabel('Value (₹)')
                ax1.legend()
                ax1.grid(True)
            
            # P&L tracking
            if self.portfolio_data:
                pnl = [d['pnl'] for d in data]
                colors = ['g' if p >= 0 else 'r' for p in pnl]
                ax2.bar(range(len(pnl)), pnl, color=colors)
                ax2.set_title('P&L Tracking')
                ax2.set_ylabel('P&L (₹)')
                ax2.grid(True)
            
            # Position distribution
            positions = portfolio_summary.get('positions', {})
            if positions:
                symbols = list(positions.keys())
                values = [pos['quantity'] * pos['current_price'] for pos in positions.values()]
                ax3.pie(values, labels=symbols, autopct='%1.1f%%')
                ax3.set_title('Position Distribution')
            
            # Performance metrics
            metrics = {
                'Total Return %': portfolio_summary.get('total_return_pct', 0),
                'Win Rate %': portfolio_summary.get('win_rate', 0),
                'Total Trades': portfolio_summary.get('total_trades', 0)
            }
            
            ax4.bar(metrics.keys(), metrics.values())
            ax4.set_title('Performance Metrics')
            ax4.tick_params(axis='x', rotation=45)
            
            plt.tight_layout()
            
            # Save chart
            summary_path = self.viz_dir / f"daily_summary_{datetime.now().strftime('%Y%m%d')}.png"
            plt.savefig(summary_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"📊 Daily summary chart saved: {summary_path}")
            
            return str(summary_path)
            
        except Exception as e:
            self.logger.error(f"Error saving daily summary chart: {e}")
            return ""

# Global visualizer instance
trading_visualizer = TradingVisualizer()
