#!/usr/bin/env python3
"""
Intraday Trading AI Agent Training
Fast trades under 5 minutes with ₹1000 budget
"""

import asyncio
import logging
import random
import time
from datetime import datetime, timedelta
from colorama import init, Fore, Style

# Initialize colorama
init()

async def start_intraday_training():
    """Start intraday training session"""
    
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}⚡ INTRADAY TRADING AI TRAINING{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Fast Trades Under 5 Minutes | ₹1000 Budget{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    
    try:
        # Import systems
        from environment.live_prediction_env import live_env, PredictionAction
        from portfolio.portfolio_manager import portfolio_manager
        from memory.persistent_memory import persistent_memory
        from visualization.trading_visualizer import trading_visualizer
        
        print(f"\n{Fore.GREEN}🚀 Initializing Intraday Trading Systems...{Style.RESET_ALL}")
        
        # Start live environment
        await live_env.start_live_environment()
        
        # Intraday trading symbols (high liquidity)
        intraday_symbols = [
            "RELIANCE.NS", "TCS.NS", "HDFCBANK.NS", "INFY.NS", 
            "ICICIBANK.NS", "SBIN.NS", "BHARTIARTL.NS", "ITC.NS"
        ]
        
        print(f"✅ Systems ready!")
        print(f"📊 Trading symbols: {len(intraday_symbols)} high-liquidity stocks")
        print(f"💰 Budget: ₹1000")
        print(f"⏱️  Trade duration: 2-5 minutes each")
        
        # Show current status
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        experiences = persistent_memory.experiences
        
        print(f"\n{Fore.BLUE}📊 CURRENT STATUS:{Style.RESET_ALL}")
        print(f"💵 Portfolio Value: ₹{portfolio_summary['total_value']:,.2f}")
        print(f"📈 Total P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
        print(f"🧠 Experiences: {len(experiences)}")
        print(f"🎯 Total Trades: {portfolio_summary['total_trades']}")
        
        # Start training
        print(f"\n{Fore.YELLOW}⚡ STARTING INTRADAY TRAINING SESSION{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Making rapid predictions every 30 seconds...{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Press Ctrl+C to stop training{Style.RESET_ALL}")
        
        predictions_made = 0
        trades_executed = 0
        start_time = datetime.now()
        
        # Rapid intraday training loop
        while True:
            try:
                # Select random symbol for intraday trading
                symbol = random.choice(intraday_symbols)
                
                # Generate intraday prediction (shorter timeframes, higher volatility)
                confidence = random.uniform(0.65, 0.95)  # Higher confidence for intraday
                predicted_change = random.uniform(-1.5, 1.5)  # Smaller moves for intraday
                
                # Intraday action logic (more aggressive)
                if predicted_change > 0.5:
                    action = PredictionAction.BUY
                elif predicted_change > 0.2:
                    action = PredictionAction.BUY
                elif predicted_change < -0.5:
                    action = PredictionAction.SELL
                elif predicted_change < -0.2:
                    action = PredictionAction.SELL
                else:
                    action = PredictionAction.HOLD
                
                # Make intraday prediction (2-3 minute timeframe)
                timeframe = random.choice([2, 3])  # Very short for intraday
                
                pred_id = live_env.make_prediction(
                    symbol=symbol,
                    action=action,
                    confidence=confidence,
                    predicted_change=predicted_change,
                    timeframe=timeframe,
                    reasoning=f"Intraday trade {predictions_made + 1}: {action.name} on {symbol}"
                )
                
                if pred_id:
                    predictions_made += 1
                    
                    # Show prediction
                    print(f"\n📊 Prediction {predictions_made}: {symbol}")
                    print(f"   Action: {action.name} | Confidence: {confidence:.1%}")
                    print(f"   Expected: {predicted_change:+.2f}% | Timeframe: {timeframe}min")
                    
                    # Check if this might trigger a trade
                    if confidence > 0.75 and abs(predicted_change) > 0.3:
                        print(f"   🎯 High confidence - likely to trigger trade!")
                    
                    # Show progress every 5 predictions
                    if predictions_made % 5 == 0:
                        await show_intraday_progress(predictions_made, start_time)
                
                # Wait 30 seconds for next prediction (rapid intraday pace)
                await asyncio.sleep(30)
                
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}⏹️  Training stopped by user{Style.RESET_ALL}")
                break
            except Exception as e:
                print(f"⚠️  Prediction error: {e}")
                await asyncio.sleep(10)
        
        # Training completed
        await intraday_training_summary(predictions_made, start_time)
        
    except Exception as e:
        print(f"{Fore.RED}❌ Training error: {e}{Style.RESET_ALL}")
    finally:
        # Stop environment
        try:
            live_env.stop()
        except:
            pass

async def show_intraday_progress(predictions_made, start_time):
    """Show intraday training progress"""
    try:
        from portfolio.portfolio_manager import portfolio_manager
        from memory.persistent_memory import persistent_memory
        
        elapsed = (datetime.now() - start_time).total_seconds() / 60
        
        print(f"\n{Fore.BLUE}⚡ INTRADAY PROGRESS{Style.RESET_ALL}")
        print(f"⏱️  Time: {elapsed:.1f} minutes")
        print(f"🎯 Predictions: {predictions_made}")
        print(f"📊 Rate: {predictions_made/elapsed:.1f} predictions/minute")
        
        # Portfolio status
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        print(f"💰 Portfolio: ₹{portfolio_summary['total_value']:,.2f}")
        print(f"📈 P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
        print(f"🔄 Trades: {portfolio_summary['total_trades']}")
        
        # Recent accuracy
        experiences = persistent_memory.experiences
        if len(experiences) >= 5:
            recent_accuracy = sum(exp.accuracy_score for exp in experiences[-5:]) / 5
            print(f"🎯 Recent Accuracy: {recent_accuracy:.1%}")
        
        # Performance assessment
        if portfolio_summary['total_pnl'] > 50:
            print(f"{Fore.GREEN}🚀 Excellent intraday performance!{Style.RESET_ALL}")
        elif portfolio_summary['total_pnl'] > 0:
            print(f"{Fore.YELLOW}📈 Positive intraday returns!{Style.RESET_ALL}")
        elif portfolio_summary['total_trades'] > 0:
            print(f"{Fore.BLUE}📊 Learning intraday patterns...{Style.RESET_ALL}")
        else:
            print(f"{Fore.CYAN}🎲 Building confidence for trades...{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"⚠️  Progress error: {e}")

async def intraday_training_summary(predictions_made, start_time):
    """Show intraday training summary"""
    
    print(f"\n{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}⚡ INTRADAY TRAINING COMPLETED!{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    
    # Duration and speed
    duration = datetime.now() - start_time
    duration_minutes = duration.total_seconds() / 60
    prediction_rate = predictions_made / duration_minutes if duration_minutes > 0 else 0
    
    print(f"\n⏱️  TRAINING METRICS:")
    print(f"Duration: {duration_minutes:.1f} minutes")
    print(f"Predictions: {predictions_made}")
    print(f"Rate: {prediction_rate:.1f} predictions/minute")
    
    try:
        # Portfolio performance
        from portfolio.portfolio_manager import portfolio_manager
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        
        print(f"\n💰 INTRADAY PORTFOLIO RESULTS:")
        print(f"💵 Final Value: ₹{portfolio_summary['total_value']:,.2f}")
        print(f"📈 Return: {portfolio_summary['total_return_pct']:+.2f}%")
        print(f"💹 Total P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
        print(f"🔄 Trades Executed: {portfolio_summary['total_trades']}")
        print(f"🏆 Win Rate: {portfolio_summary['win_rate']:.1f}%")
        
        # Intraday performance assessment
        if portfolio_summary['total_return_pct'] > 5:
            print(f"\n{Fore.GREEN}🎉 OUTSTANDING intraday performance!{Style.RESET_ALL}")
            print(f"🚀 Agent is mastering fast intraday trading!")
        elif portfolio_summary['total_return_pct'] > 2:
            print(f"\n{Fore.YELLOW}📈 EXCELLENT intraday returns!{Style.RESET_ALL}")
            print(f"⚡ Agent is learning fast trading patterns!")
        elif portfolio_summary['total_return_pct'] > 0:
            print(f"\n{Fore.BLUE}📊 POSITIVE intraday performance!{Style.RESET_ALL}")
            print(f"🎯 Agent is building intraday skills!")
        else:
            print(f"\n{Fore.CYAN}🧠 LEARNING intraday patterns...{Style.RESET_ALL}")
            print(f"📚 Agent needs more intraday experience!")
        
        # Memory analysis
        from memory.persistent_memory import persistent_memory
        experiences = persistent_memory.experiences
        
        print(f"\n🧠 LEARNING ANALYSIS:")
        print(f"📚 Total Experiences: {len(experiences)}")
        
        if experiences:
            # Overall accuracy
            total_accuracy = sum(exp.accuracy_score for exp in experiences) / len(experiences)
            print(f"🎯 Overall Accuracy: {total_accuracy:.1%}")
            
            # Recent vs early performance
            if len(experiences) >= 10:
                early_accuracy = sum(exp.accuracy_score for exp in experiences[:5]) / 5
                recent_accuracy = sum(exp.accuracy_score for exp in experiences[-5:]) / 5
                improvement = recent_accuracy - early_accuracy
                
                print(f"📈 Recent Accuracy: {recent_accuracy:.1%}")
                print(f"📊 Improvement: {improvement:+.1%}")
                
                if improvement > 0.1:
                    print(f"{Fore.GREEN}🚀 Rapid learning progress!{Style.RESET_ALL}")
                elif improvement > 0:
                    print(f"{Fore.YELLOW}📈 Steady improvement!{Style.RESET_ALL}")
        
        # Generate intraday visualizations
        print(f"\n📊 Generating intraday visualizations...")
        from visualization.trading_visualizer import trading_visualizer
        
        # Create performance charts
        trading_visualizer.create_portfolio_performance_chart()
        summary_path = trading_visualizer.save_daily_summary_chart()
        
        print(f"✅ Intraday charts created!")
        
        # Show positions
        positions = portfolio_summary.get('positions', {})
        if positions:
            print(f"\n📈 CURRENT POSITIONS:")
            for symbol, pos in positions.items():
                pnl_color = Fore.GREEN if pos['unrealized_pnl'] >= 0 else Fore.RED
                print(f"{symbol}: {pos['quantity']} shares @ ₹{pos['avg_buy_price']:.2f}")
                print(f"   Current: ₹{pos['current_price']:.2f} | "
                      f"P&L: {pnl_color}₹{pos['unrealized_pnl']:+.2f}{Style.RESET_ALL}")
        
        # Intraday recommendations
        print(f"\n💡 INTRADAY RECOMMENDATIONS:")
        
        if portfolio_summary['total_trades'] == 0:
            print("1. 🎯 Increase prediction confidence to trigger trades")
            print("2. ⚡ Focus on high-volatility stocks for intraday")
            print("3. 📊 Continue training to build pattern recognition")
        elif portfolio_summary['win_rate'] < 50:
            print("1. 🎲 Improve prediction accuracy for better trades")
            print("2. ⏱️  Consider shorter timeframes (1-2 minutes)")
            print("3. 📈 Focus on trending stocks for intraday")
        else:
            print("1. 🚀 Excellent performance! Continue current strategy")
            print("2. 💰 Consider increasing position sizes")
            print("3. 🔄 Expand to more intraday symbols")
        
    except Exception as e:
        print(f"⚠️  Summary error: {e}")
    
    print(f"\n{Fore.GREEN}⚡ Intraday training session completed!{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Your AI agent is now trained for fast intraday trading!{Style.RESET_ALL}")

if __name__ == "__main__":
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Run intraday training
    asyncio.run(start_intraday_training())
