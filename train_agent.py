#!/usr/bin/env python3
"""
AI Agent Training Script
Step-by-step training with visualization and memory
"""

import asyncio
import logging
import random
import time
from datetime import datetime, timedelta
from colorama import init, Fore, Style

# Initialize colorama
init()

# Import our systems
from environment.live_prediction_env import live_env
from portfolio.portfolio_manager import portfolio_manager
from visualization.trading_visualizer import trading_visualizer
from memory.persistent_memory import persistent_memory
from data.market_data_fetcher import get_market_data_fetcher

def print_training_banner():
    """Print training banner"""
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🤖 AI AGENT TRAINING SYSTEM{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Memory-Enhanced | Visual Feedback | ₹1000 Budget{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")

def show_training_options():
    """Show training options"""
    print(f"\n{Fore.CYAN}🎯 TRAINING OPTIONS:{Style.RESET_ALL}")
    print(f"1. 🚀 Quick Training (10 predictions, 5 minutes)")
    print(f"2. 📈 Standard Training (50 predictions, 30 minutes)")
    print(f"3. 🏆 Intensive Training (100 predictions, 1 hour)")
    print(f"4. 🔄 Continuous Training (Until stopped)")
    print(f"5. 📊 Check Current Progress")
    print(f"6. 🧠 View Memory Insights")
    print(f"7. 💰 Check Portfolio Status")
    print(f"8. 📈 Open Visualizations")
    print(f"9. ❓ Training Help")
    print(f"10. 🚪 Exit")

async def quick_training():
    """Quick training session - 10 predictions"""
    print(f"\n{Fore.YELLOW}🚀 Starting Quick Training Session{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Target: 10 predictions in ~5 minutes{Style.RESET_ALL}")
    
    await run_training_session(
        target_predictions=10,
        session_name="Quick Training",
        prediction_interval=30,  # 30 seconds between predictions
        max_duration=300  # 5 minutes max
    )

async def standard_training():
    """Standard training session - 50 predictions"""
    print(f"\n{Fore.YELLOW}📈 Starting Standard Training Session{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Target: 50 predictions in ~30 minutes{Style.RESET_ALL}")
    
    await run_training_session(
        target_predictions=50,
        session_name="Standard Training",
        prediction_interval=35,  # 35 seconds between predictions
        max_duration=1800  # 30 minutes max
    )

async def intensive_training():
    """Intensive training session - 100 predictions"""
    print(f"\n{Fore.YELLOW}🏆 Starting Intensive Training Session{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Target: 100 predictions in ~1 hour{Style.RESET_ALL}")
    
    await run_training_session(
        target_predictions=100,
        session_name="Intensive Training",
        prediction_interval=30,  # 30 seconds between predictions
        max_duration=3600  # 1 hour max
    )

async def continuous_training():
    """Continuous training until stopped"""
    print(f"\n{Fore.YELLOW}🔄 Starting Continuous Training{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Will run until you press Ctrl+C{Style.RESET_ALL}")
    
    await run_training_session(
        target_predictions=float('inf'),
        session_name="Continuous Training",
        prediction_interval=45,  # 45 seconds between predictions
        max_duration=float('inf')
    )

async def run_training_session(target_predictions: int, session_name: str, 
                              prediction_interval: int, max_duration: int):
    """Run a training session with specified parameters"""
    
    print(f"\n{Fore.CYAN}🎯 {session_name} Configuration:{Style.RESET_ALL}")
    print(f"Target Predictions: {target_predictions}")
    print(f"Prediction Interval: {prediction_interval} seconds")
    print(f"Max Duration: {max_duration/60:.1f} minutes")
    print(f"Budget: ₹1000")
    
    # Confirm start
    start = input(f"\n{Fore.CYAN}Start training? (y/n): {Style.RESET_ALL}").lower()
    if start != 'y':
        print(f"{Fore.YELLOW}Training cancelled{Style.RESET_ALL}")
        return
    
    # Initialize training
    print(f"\n{Fore.GREEN}🚀 Initializing Training Environment...{Style.RESET_ALL}")
    
    try:
        # Start live environment
        await live_env.start_live_environment()
        
        # Training symbols (Indian stocks with good liquidity)
        training_symbols = [
            "RELIANCE.NS", "TCS.NS", "HDFCBANK.NS", "INFY.NS", "ICICIBANK.NS",
            "SBIN.NS", "BHARTIARTL.NS", "ITC.NS", "KOTAKBANK.NS", "LT.NS"
        ]
        
        # Training statistics
        predictions_made = 0
        start_time = datetime.now()
        session_stats = {
            'predictions': 0,
            'trades_executed': 0,
            'portfolio_changes': [],
            'accuracy_scores': [],
            'confidence_levels': []
        }
        
        print(f"\n{Fore.GREEN}✅ Training Started!{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Press Ctrl+C to stop training early{Style.RESET_ALL}")
        
        # Main training loop
        while predictions_made < target_predictions:
            try:
                # Check time limit
                elapsed = (datetime.now() - start_time).total_seconds()
                if elapsed > max_duration:
                    print(f"\n⏰ Time limit reached ({max_duration/60:.1f} minutes)")
                    break
                
                # Select random symbol for prediction
                symbol = random.choice(training_symbols)
                
                # Get market data
                fetcher = get_market_data_fetcher()
                market_data = await fetcher.fetch_realtime_data(symbol)
                
                if not market_data:
                    print(f"⚠️  No data for {symbol}, skipping...")
                    await asyncio.sleep(5)
                    continue
                
                # Generate AI prediction (simulated for training)
                confidence = random.uniform(0.5, 0.95)
                predicted_change = random.uniform(-3.0, 3.0)
                
                # Determine action based on predicted change
                if predicted_change > 1.5:
                    action = "STRONG_BUY"
                elif predicted_change > 0.5:
                    action = "BUY"
                elif predicted_change < -1.5:
                    action = "STRONG_SELL"
                elif predicted_change < -0.5:
                    action = "SELL"
                else:
                    action = "HOLD"
                
                # Make prediction
                pred_id = live_env.make_prediction(
                    symbol=symbol,
                    action=getattr(live_env.PredictionAction, action, live_env.PredictionAction.HOLD),
                    confidence=confidence,
                    predicted_change=predicted_change,
                    timeframe=2,  # 2 minutes for quick training
                    reasoning=f"Training prediction {predictions_made + 1}: {action} based on technical analysis"
                )
                
                if pred_id:
                    predictions_made += 1
                    session_stats['predictions'] = predictions_made
                    session_stats['confidence_levels'].append(confidence)
                    
                    # Show progress
                    progress = (predictions_made / target_predictions) * 100 if target_predictions != float('inf') else 0
                    print(f"\n📊 Prediction {predictions_made}: {symbol} | {action} | "
                          f"Confidence: {confidence:.1%} | Progress: {progress:.1f}%")
                    
                    # Show portfolio status every 10 predictions
                    if predictions_made % 10 == 0:
                        await show_training_progress(session_stats, start_time)
                
                # Wait before next prediction
                await asyncio.sleep(prediction_interval)
                
            except KeyboardInterrupt:
                print(f"\n{Fore.YELLOW}Training stopped by user{Style.RESET_ALL}")
                break
            except Exception as e:
                print(f"⚠️  Training error: {e}")
                await asyncio.sleep(5)
        
        # Training completed
        await training_completed(session_stats, start_time, session_name)
        
    except Exception as e:
        print(f"{Fore.RED}❌ Training failed: {e}{Style.RESET_ALL}")
    finally:
        # Stop live environment
        live_env.stop()

async def show_training_progress(session_stats: dict, start_time: datetime):
    """Show current training progress"""
    
    print(f"\n{Fore.BLUE}📊 TRAINING PROGRESS{Style.RESET_ALL}")
    
    # Time elapsed
    elapsed = datetime.now() - start_time
    print(f"⏱️  Time Elapsed: {elapsed.total_seconds()/60:.1f} minutes")
    
    # Predictions made
    print(f"🎯 Predictions Made: {session_stats['predictions']}")
    
    # Portfolio status
    portfolio_summary = portfolio_manager.get_portfolio_summary()
    print(f"💰 Portfolio Value: ₹{portfolio_summary['total_value']:,.2f}")
    print(f"📈 P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
    print(f"🎯 Total Trades: {portfolio_summary['total_trades']}")
    
    # Average confidence
    if session_stats['confidence_levels']:
        avg_confidence = sum(session_stats['confidence_levels']) / len(session_stats['confidence_levels'])
        print(f"🎲 Average Confidence: {avg_confidence:.1%}")
    
    # Memory insights
    try:
        experiences = persistent_memory.experiences
        if experiences:
            recent_accuracy = sum(exp.accuracy_score for exp in experiences[-10:]) / min(10, len(experiences))
            print(f"🧠 Recent Accuracy: {recent_accuracy:.1%}")
    except:
        pass

async def training_completed(session_stats: dict, start_time: datetime, session_name: str):
    """Handle training completion"""
    
    print(f"\n{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🎉 {session_name} COMPLETED!{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*60}{Style.RESET_ALL}")
    
    # Training summary
    elapsed = datetime.now() - start_time
    print(f"\n📊 TRAINING SUMMARY:")
    print(f"⏱️  Duration: {elapsed.total_seconds()/60:.1f} minutes")
    print(f"🎯 Predictions Made: {session_stats['predictions']}")
    
    # Portfolio performance
    portfolio_summary = portfolio_manager.get_portfolio_summary()
    print(f"\n💰 PORTFOLIO PERFORMANCE:")
    print(f"💵 Final Value: ₹{portfolio_summary['total_value']:,.2f}")
    print(f"📈 Total Return: {portfolio_summary['total_return_pct']:+.2f}%")
    print(f"💹 Total P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
    print(f"🎯 Total Trades: {portfolio_summary['total_trades']}")
    print(f"🏆 Win Rate: {portfolio_summary['win_rate']:.1f}%")
    
    # Memory insights
    try:
        experiences = persistent_memory.experiences
        if experiences:
            total_accuracy = sum(exp.accuracy_score for exp in experiences) / len(experiences)
            print(f"\n🧠 LEARNING PROGRESS:")
            print(f"📚 Total Experiences: {len(experiences)}")
            print(f"🎯 Overall Accuracy: {total_accuracy:.1%}")
            
            # Show improvement
            if len(experiences) >= 20:
                early_accuracy = sum(exp.accuracy_score for exp in experiences[:10]) / 10
                recent_accuracy = sum(exp.accuracy_score for exp in experiences[-10:]) / 10
                improvement = recent_accuracy - early_accuracy
                print(f"📈 Improvement: {improvement:+.1%}")
    except Exception as e:
        print(f"⚠️  Memory analysis error: {e}")
    
    # Generate visualizations
    print(f"\n📊 Generating Training Visualizations...")
    try:
        # Create performance dashboard
        trading_visualizer.create_portfolio_performance_chart()
        
        # Save daily summary
        summary_path = trading_visualizer.save_daily_summary_chart()
        
        print(f"✅ Visualizations created!")
        
        # Ask to open visualizations
        open_viz = input(f"\n{Fore.CYAN}Open training visualizations? (y/n): {Style.RESET_ALL}").lower()
        if open_viz == 'y':
            import webbrowser
            from pathlib import Path
            
            chart_dir = Path("visualization/charts")
            recent_files = sorted(chart_dir.glob("*.html"), key=lambda x: x.stat().st_mtime)[-2:]
            
            for file in recent_files:
                webbrowser.open(f"file://{file.absolute()}")
                print(f"🌐 Opened: {file.name}")
    
    except Exception as e:
        print(f"⚠️  Visualization error: {e}")
    
    print(f"\n{Fore.GREEN}🎓 Training session completed successfully!{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Your AI agent is now smarter and ready for more training!{Style.RESET_ALL}")

def check_current_progress():
    """Check current training progress"""
    print(f"\n{Fore.YELLOW}📊 Current Training Progress{Style.RESET_ALL}")
    
    # Portfolio status
    portfolio_summary = portfolio_manager.get_portfolio_summary()
    print(f"\n💰 PORTFOLIO STATUS:")
    print(f"💵 Current Value: ₹{portfolio_summary['total_value']:,.2f}")
    print(f"📈 Total Return: {portfolio_summary['total_return_pct']:+.2f}%")
    print(f"💹 Total P&L: ₹{portfolio_summary['total_pnl']:+,.2f}")
    print(f"🎯 Total Trades: {portfolio_summary['total_trades']}")
    print(f"🏆 Win Rate: {portfolio_summary['win_rate']:.1f}%")
    
    # Memory status
    try:
        experiences = persistent_memory.experiences
        print(f"\n🧠 LEARNING STATUS:")
        print(f"📚 Total Experiences: {len(experiences)}")
        
        if experiences:
            total_accuracy = sum(exp.accuracy_score for exp in experiences) / len(experiences)
            print(f"🎯 Overall Accuracy: {total_accuracy:.1%}")
            
            # Recent performance
            if len(experiences) >= 10:
                recent_accuracy = sum(exp.accuracy_score for exp in experiences[-10:]) / 10
                print(f"📈 Recent Accuracy: {recent_accuracy:.1%}")
        
        # Patterns discovered
        patterns = persistent_memory.patterns
        print(f"🔍 Patterns Discovered: {len(patterns)}")
        
    except Exception as e:
        print(f"⚠️  Memory status error: {e}")

def view_memory_insights():
    """View memory insights and recommendations"""
    print(f"\n{Fore.YELLOW}🧠 Memory Insights{Style.RESET_ALL}")
    
    try:
        from memory.memory_visualizer import memory_visualizer
        
        # Generate memory report
        report = memory_visualizer.generate_memory_report()
        
        # Show key insights
        summary = report.get('summary', {})
        print(f"\n📊 MEMORY SUMMARY:")
        print(f"📚 Total Experiences: {summary.get('total_experiences', 0)}")
        print(f"🎯 Overall Accuracy: {summary.get('overall_accuracy', 0):.1%}")
        print(f"💾 Memory Size: {summary.get('memory_size_mb', 0):.2f} MB")
        
        # Sector performance
        sector_perf = summary.get('sector_performance', {})
        if sector_perf:
            print(f"\n🏭 SECTOR PERFORMANCE:")
            sorted_sectors = sorted(sector_perf.items(), key=lambda x: x[1]['accuracy'], reverse=True)
            for sector, perf in sorted_sectors[:5]:  # Top 5
                print(f"{sector}: {perf['accuracy']:.1%} accuracy ({perf['count']} trades)")
        
        # AI recommendations
        recommendations = report.get('recommendations', [])
        if recommendations:
            print(f"\n💡 AI RECOMMENDATIONS:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"{i}. {rec}")
        
    except Exception as e:
        print(f"⚠️  Memory insights error: {e}")

def show_training_help():
    """Show training help"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📚 AI AGENT TRAINING HELP{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.GREEN}🎯 Training Process:{Style.RESET_ALL}")
    print("1. Agent makes predictions on real stock data")
    print("2. Predictions are tracked and resolved automatically")
    print("3. Successful predictions trigger portfolio trades")
    print("4. Every experience is stored in persistent memory")
    print("5. AI learns patterns and improves over time")
    
    print(f"\n{Fore.GREEN}📊 Training Options:{Style.RESET_ALL}")
    print("• Quick Training: 10 predictions, good for testing")
    print("• Standard Training: 50 predictions, balanced learning")
    print("• Intensive Training: 100 predictions, deep learning")
    print("• Continuous Training: Runs until stopped")
    
    print(f"\n{Fore.GREEN}💰 Portfolio Management:{Style.RESET_ALL}")
    print("• Starts with ₹1000 virtual budget")
    print("• Only high-accuracy predictions trigger trades")
    print("• Risk management prevents over-exposure")
    print("• Real-time P&L tracking and visualization")
    
    print(f"\n{Fore.GREEN}🧠 Memory System:{Style.RESET_ALL}")
    print("• Every prediction and outcome is remembered")
    print("• Patterns are discovered automatically")
    print("• NLP insights improve understanding")
    print("• Memory enhances future predictions")

async def main():
    """Main training function"""
    print_training_banner()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    while True:
        show_training_options()
        
        try:
            choice = input(f"\n{Fore.CYAN}Choose training option (1-10): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                await quick_training()
            elif choice == "2":
                await standard_training()
            elif choice == "3":
                await intensive_training()
            elif choice == "4":
                await continuous_training()
            elif choice == "5":
                check_current_progress()
            elif choice == "6":
                view_memory_insights()
            elif choice == "7":
                portfolio_summary = portfolio_manager.get_portfolio_summary()
                print(f"\n💰 Portfolio: ₹{portfolio_summary['total_value']:,.2f} | "
                      f"P&L: ₹{portfolio_summary['total_pnl']:+,.2f} | "
                      f"Trades: {portfolio_summary['total_trades']}")
            elif choice == "8":
                import webbrowser
                from pathlib import Path
                chart_dir = Path("visualization/charts")
                if chart_dir.exists():
                    files = list(chart_dir.glob("*.html"))
                    if files:
                        latest = max(files, key=lambda x: x.stat().st_mtime)
                        webbrowser.open(f"file://{latest.absolute()}")
                        print(f"🌐 Opened: {latest.name}")
                    else:
                        print("No visualization files found. Run training first!")
                else:
                    print("No visualization directory found. Run training first!")
            elif choice == "9":
                show_training_help()
            elif choice == "10":
                print(f"\n{Fore.YELLOW}🎓 Happy Training! Your AI agent's progress is saved!{Style.RESET_ALL}")
                break
            else:
                print(f"{Fore.RED}❌ Invalid choice. Please enter 1-10.{Style.RESET_ALL}")
        
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}🎓 Training session ended. Progress saved!{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    asyncio.run(main())
