#!/usr/bin/env python3
"""
Complete Visual Trading AI Agent
Real stock graphs, predictions, P&L tracking with ₹1000 budget
"""

import asyncio
import sys
import subprocess
import os
import webbrowser
from pathlib import Path
from colorama import init, Fore, Style
from datetime import datetime

# Initialize colorama
init()

def print_banner():
    """Print the startup banner"""
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📊 VISUAL TRADING AI AGENT{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Real Stock Graphs | Live Predictions | ₹1000 Budget | P&L Tracking{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")

def show_features():
    """Show visualization features"""
    print(f"\n{Fore.CYAN}📊 VISUALIZATION FEATURES:{Style.RESET_ALL}")
    print(f"✅ {Fore.GREEN}Real Stock Price Charts{Style.RESET_ALL} - Live candlestick charts with technical indicators")
    print(f"✅ {Fore.GREEN}AI Prediction Overlay{Style.RESET_ALL} - See predictions on actual price charts")
    print(f"✅ {Fore.GREEN}Portfolio Management{Style.RESET_ALL} - ₹1000 budget with real trade execution")
    print(f"✅ {Fore.GREEN}P&L Tracking{Style.RESET_ALL} - Real-time profit/loss monitoring")
    print(f"✅ {Fore.GREEN}Interactive Dashboards{Style.RESET_ALL} - Live updating charts and metrics")
    print(f"✅ {Fore.GREEN}Performance Analytics{Style.RESET_ALL} - Win rate, returns, trade analysis")
    print(f"✅ {Fore.GREEN}Memory Integration{Style.RESET_ALL} - Persistent learning with NLP insights")

def show_menu():
    """Show the main menu"""
    print(f"\n{Fore.CYAN}📋 CHOOSE YOUR OPTION:{Style.RESET_ALL}")
    print(f"1. 🎬 Demo Complete System (See everything in action)")
    print(f"2. 📊 View Live Stock Charts")
    print(f"3. 💰 Check Portfolio Status (₹1000 budget)")
    print(f"4. 🚀 Start Visual Trading Agent")
    print(f"5. 📈 Create Performance Dashboard")
    print(f"6. 🎯 Live Trading Simulation")
    print(f"7. 🧠 Memory + Visualization Demo")
    print(f"8. 📁 Open Visualization Files")
    print(f"9. 💡 Show Trading Insights")
    print(f"10. ❓ Help & Documentation")
    print(f"11. 🚪 Exit")

def run_complete_demo():
    """Run the complete visualization demo"""
    print(f"\n{Fore.YELLOW}🎬 Starting Complete Visualization Demo...{Style.RESET_ALL}")
    try:
        subprocess.run([sys.executable, "demo_visualization.py"], check=True)
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Demo failed to run{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Demo stopped by user{Style.RESET_ALL}")

def view_stock_charts():
    """View live stock charts"""
    print(f"\n{Fore.YELLOW}📊 Creating Live Stock Charts...{Style.RESET_ALL}")
    
    try:
        from visualization.trading_visualizer import trading_visualizer
        
        # Get symbols from user
        print(f"{Fore.CYAN}Enter stock symbols (e.g., RELIANCE.NS TCS.NS HDFCBANK.NS):{Style.RESET_ALL}")
        symbols_input = input("Symbols: ").strip()
        
        if symbols_input:
            symbols = symbols_input.split()
        else:
            symbols = ["RELIANCE.NS", "TCS.NS", "HDFCBANK.NS"]  # Default
        
        print(f"Creating charts for: {', '.join(symbols)}")
        
        # Create charts for each symbol
        chart_files = []
        for symbol in symbols:
            print(f"📈 Creating chart for {symbol}...")
            fig = trading_visualizer.create_stock_chart_with_predictions(symbol)
            if fig:
                # Find the latest chart file
                chart_dir = Path("visualization/charts")
                chart_files.extend(list(chart_dir.glob(f"{symbol}_chart_*.html")))
        
        # Open charts in browser
        if chart_files:
            print(f"\n✅ Created {len(chart_files)} charts")
            open_charts = input(f"{Fore.CYAN}Open charts in browser? (y/n): {Style.RESET_ALL}").lower()
            
            if open_charts == 'y':
                for chart_file in chart_files[-3:]:  # Open last 3
                    webbrowser.open(f"file://{chart_file.absolute()}")
                    print(f"🌐 Opened: {chart_file.name}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error creating charts: {e}{Style.RESET_ALL}")

def check_portfolio_status():
    """Check current portfolio status"""
    print(f"\n{Fore.YELLOW}💰 Portfolio Status (₹1000 Budget){Style.RESET_ALL}")
    
    try:
        from portfolio.portfolio_manager import portfolio_manager
        
        summary = portfolio_manager.get_portfolio_summary()
        
        print(f"\n{Fore.GREEN}📊 PORTFOLIO OVERVIEW{Style.RESET_ALL}")
        print(f"💵 Initial Capital: ₹{summary['initial_capital']:,.2f}")
        print(f"💰 Current Value: ₹{summary['total_value']:,.2f}")
        print(f"💸 Cash Balance: ₹{summary['cash_balance']:,.2f}")
        print(f"📈 Positions Value: ₹{summary['positions_value']:,.2f}")
        print(f"💹 Total P&L: ₹{summary['total_pnl']:+,.2f}")
        print(f"📊 Return: {summary['total_return_pct']:+.2f}%")
        print(f"🎯 Total Trades: {summary['total_trades']}")
        print(f"🏆 Win Rate: {summary['win_rate']:.1f}%")
        
        # Show positions
        positions = summary.get('positions', {})
        if positions:
            print(f"\n{Fore.BLUE}📈 CURRENT POSITIONS{Style.RESET_ALL}")
            for symbol, pos in positions.items():
                pnl_color = Fore.GREEN if pos['unrealized_pnl'] >= 0 else Fore.RED
                print(f"{symbol}: {pos['quantity']} shares @ ₹{pos['avg_buy_price']:.2f}")
                print(f"   Current: ₹{pos['current_price']:.2f} | "
                      f"P&L: {pnl_color}₹{pos['unrealized_pnl']:+.2f}{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.YELLOW}📝 No current positions{Style.RESET_ALL}")
        
        # Show recent trades
        recent_trades = summary.get('recent_trades', [])
        if recent_trades:
            print(f"\n{Fore.MAGENTA}📋 RECENT TRADES{Style.RESET_ALL}")
            for trade in recent_trades[-5:]:  # Last 5 trades
                action_color = Fore.GREEN if trade['action'] == 'BUY' else Fore.RED
                print(f"{action_color}{trade['action']}{Style.RESET_ALL}: "
                      f"{trade['quantity']} {trade['symbol']} @ ₹{trade['price']:.2f}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error checking portfolio: {e}{Style.RESET_ALL}")

def start_visual_trading_agent():
    """Start the visual trading agent"""
    print(f"\n{Fore.YELLOW}🚀 Starting Visual Trading Agent...{Style.RESET_ALL}")
    print(f"{Fore.GREEN}This will start live trading with:{Style.RESET_ALL}")
    print("• Real-time stock price monitoring")
    print("• AI predictions with visualization")
    print("• Automatic trade execution with ₹1000 budget")
    print("• Live P&L tracking and charts")
    print("• Memory-enhanced decision making")
    
    print(f"\n{Fore.CYAN}Press Ctrl+C to stop the agent{Style.RESET_ALL}\n")
    
    try:
        subprocess.run([sys.executable, "main.py"], check=True)
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Agent failed to start{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Agent stopped by user{Style.RESET_ALL}")

def create_performance_dashboard():
    """Create performance dashboard"""
    print(f"\n{Fore.YELLOW}📈 Creating Performance Dashboard...{Style.RESET_ALL}")
    
    try:
        from visualization.trading_visualizer import trading_visualizer
        
        # Create portfolio performance chart
        perf_fig = trading_visualizer.create_portfolio_performance_chart()
        
        # Create live dashboard
        symbols = ["RELIANCE.NS", "TCS.NS", "HDFCBANK.NS"]
        dashboard_fig = trading_visualizer.create_live_dashboard(symbols)
        
        # Save daily summary
        summary_path = trading_visualizer.save_daily_summary_chart()
        
        print(f"✅ Performance dashboard created!")
        
        # Show files created
        chart_dir = Path("visualization/charts")
        recent_files = sorted(chart_dir.glob("*.html"), key=lambda x: x.stat().st_mtime)[-3:]
        
        print(f"\n{Fore.GREEN}📊 Dashboard Files:{Style.RESET_ALL}")
        for file in recent_files:
            print(f"📈 {file.name}")
        
        # Open in browser
        open_dashboard = input(f"\n{Fore.CYAN}Open dashboard in browser? (y/n): {Style.RESET_ALL}").lower()
        
        if open_dashboard == 'y' and recent_files:
            webbrowser.open(f"file://{recent_files[-1].absolute()}")
            print(f"🌐 Opened dashboard in browser")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error creating dashboard: {e}{Style.RESET_ALL}")

def run_live_simulation():
    """Run live trading simulation"""
    print(f"\n{Fore.YELLOW}🎯 Live Trading Simulation{Style.RESET_ALL}")
    print(f"{Fore.GREEN}This will simulate live trading with visualization{Style.RESET_ALL}")
    
    duration = input(f"\n{Fore.CYAN}Simulation duration in minutes (default 5): {Style.RESET_ALL}").strip()
    if not duration:
        duration = "5"
    
    print(f"\n🚀 Starting {duration}-minute simulation...")
    print(f"{Fore.CYAN}Press Ctrl+C to stop early{Style.RESET_ALL}\n")
    
    try:
        # This would run a time-limited version of the trading agent
        subprocess.run([
            sys.executable, "-c", 
            f"""
import asyncio
import sys
sys.path.append('.')
from demo_visualization import demo_live_trading_simulation
asyncio.run(demo_live_trading_simulation())
"""
        ], check=True, timeout=int(duration)*60)
        
    except subprocess.TimeoutExpired:
        print(f"\n⏰ Simulation completed after {duration} minutes")
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Simulation failed{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Simulation stopped by user{Style.RESET_ALL}")

def run_memory_visualization_demo():
    """Run memory + visualization demo"""
    print(f"\n{Fore.YELLOW}🧠 Memory + Visualization Demo{Style.RESET_ALL}")
    
    try:
        # Run memory demo first
        print("1️⃣ Running memory system demo...")
        subprocess.run([sys.executable, "demo_memory.py"], check=True)
        
        print("\n2️⃣ Running visualization demo...")
        subprocess.run([sys.executable, "demo_visualization.py"], check=True)
        
        print(f"\n✅ Combined demo completed!")
        
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Demo failed{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Demo stopped by user{Style.RESET_ALL}")

def open_visualization_files():
    """Open visualization files directory"""
    print(f"\n{Fore.YELLOW}📁 Opening Visualization Files...{Style.RESET_ALL}")
    
    try:
        chart_dir = Path("visualization/charts")
        
        if not chart_dir.exists():
            print(f"{Fore.YELLOW}No visualization files found. Run a demo first!{Style.RESET_ALL}")
            return
        
        files = list(chart_dir.glob("*"))
        
        if not files:
            print(f"{Fore.YELLOW}No visualization files found. Run a demo first!{Style.RESET_ALL}")
            return
        
        print(f"\n{Fore.GREEN}📊 Available Files:{Style.RESET_ALL}")
        for i, file in enumerate(files, 1):
            file_type = "📈 Chart" if file.suffix == ".html" else "🖼️  Image"
            print(f"{i}. {file_type}: {file.name}")
        
        choice = input(f"\n{Fore.CYAN}Enter file number to open (or 'all' for directory): {Style.RESET_ALL}").strip()
        
        if choice.lower() == 'all':
            # Open directory
            if sys.platform == "win32":
                os.startfile(chart_dir)
            elif sys.platform == "darwin":
                subprocess.run(["open", chart_dir])
            else:
                subprocess.run(["xdg-open", chart_dir])
            print(f"📁 Opened visualization directory")
        
        elif choice.isdigit() and 1 <= int(choice) <= len(files):
            file_to_open = files[int(choice) - 1]
            webbrowser.open(f"file://{file_to_open.absolute()}")
            print(f"🌐 Opened: {file_to_open.name}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error opening files: {e}{Style.RESET_ALL}")

def show_trading_insights():
    """Show trading insights from memory and portfolio"""
    print(f"\n{Fore.YELLOW}💡 Trading Insights{Style.RESET_ALL}")
    
    try:
        # Portfolio insights
        from portfolio.portfolio_manager import portfolio_manager
        summary = portfolio_manager.get_portfolio_summary()
        
        print(f"\n{Fore.GREEN}💰 PORTFOLIO INSIGHTS{Style.RESET_ALL}")
        
        if summary['total_trades'] > 0:
            print(f"📊 Performance: {summary['total_return_pct']:+.2f}% return")
            print(f"🎯 Success Rate: {summary['win_rate']:.1f}% win rate")
            
            if summary['total_return_pct'] > 5:
                print(f"🎉 Excellent performance! Strategy is working well.")
            elif summary['total_return_pct'] > 0:
                print(f"📈 Positive returns. Keep refining the strategy.")
            else:
                print(f"📉 Negative returns. Consider strategy adjustments.")
        else:
            print(f"📝 No trades yet. Start trading to generate insights!")
        
        # Memory insights
        try:
            from memory.memory_visualizer import memory_visualizer
            report = memory_visualizer.generate_memory_report()
            
            recommendations = report.get('recommendations', [])
            if recommendations:
                print(f"\n{Fore.BLUE}🧠 AI RECOMMENDATIONS{Style.RESET_ALL}")
                for i, rec in enumerate(recommendations[:3], 1):
                    print(f"{i}. {rec}")
        except:
            print(f"\n{Fore.YELLOW}🧠 No memory insights available yet{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ Error generating insights: {e}{Style.RESET_ALL}")

def show_help():
    """Show help and documentation"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📚 VISUAL TRADING AI HELP{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.GREEN}📊 What is the Visual Trading System?{Style.RESET_ALL}")
    print("A complete AI trading system with beautiful visualizations:")
    print("• Real-time stock price charts with candlesticks")
    print("• AI prediction overlays on actual price data")
    print("• Portfolio management with ₹1000 virtual budget")
    print("• Live P&L tracking and performance metrics")
    print("• Interactive dashboards and analytics")
    print("• Memory-enhanced decision making")
    
    print(f"\n{Fore.GREEN}💰 Portfolio Management:{Style.RESET_ALL}")
    print("• Starts with ₹1000 virtual budget")
    print("• Executes real trades based on AI predictions")
    print("• Tracks profit/loss in real-time")
    print("• Calculates returns, win rates, and performance")
    print("• Manages risk with position sizing")
    
    print(f"\n{Fore.GREEN}📈 Visualizations Created:{Style.RESET_ALL}")
    print("• Stock charts with technical indicators")
    print("• Portfolio performance dashboards")
    print("• P&L tracking charts")
    print("• Live trading activity monitors")
    print("• Daily summary reports")
    
    print(f"\n{Fore.GREEN}🎯 How to Use:{Style.RESET_ALL}")
    print("1. Run demo to see system in action")
    print("2. Check portfolio status regularly")
    print("3. View charts to analyze performance")
    print("4. Start live agent for real trading")
    print("5. Monitor P&L and adjust strategy")

def main():
    """Main function"""
    print_banner()
    show_features()
    
    while True:
        show_menu()
        
        try:
            choice = input(f"\n{Fore.CYAN}Enter your choice (1-11): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                run_complete_demo()
            
            elif choice == "2":
                view_stock_charts()
            
            elif choice == "3":
                check_portfolio_status()
            
            elif choice == "4":
                start_visual_trading_agent()
            
            elif choice == "5":
                create_performance_dashboard()
            
            elif choice == "6":
                run_live_simulation()
            
            elif choice == "7":
                run_memory_visualization_demo()
            
            elif choice == "8":
                open_visualization_files()
            
            elif choice == "9":
                show_trading_insights()
            
            elif choice == "10":
                show_help()
            
            elif choice == "11":
                print(f"\n{Fore.YELLOW}👋 Happy Trading! Your portfolio and charts are saved!{Style.RESET_ALL}")
                break
            
            else:
                print(f"{Fore.RED}❌ Invalid choice. Please enter 1-11.{Style.RESET_ALL}")
        
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}👋 Happy Trading! Your portfolio and charts are saved!{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
