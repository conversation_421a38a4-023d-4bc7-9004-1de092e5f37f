"""
Advanced Market Analysis Tools
AI-powered technical analysis and market intelligence
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from datetime import datetime, timedelta
import asyncio
import aiohttp
from dataclasses import dataclass
import ta
import yfinance as yf

from config.indian_stocks import SECTORS, INDICES
from config.settings import settings

@dataclass
class TechnicalSignal:
    """Technical analysis signal"""
    indicator: str
    signal: str  # "BUY", "SELL", "HOLD"
    strength: float  # 0.0 to 1.0
    value: float
    description: str

@dataclass
class MarketAnalysis:
    """Comprehensive market analysis result"""
    symbol: str
    timestamp: datetime
    technical_signals: List[TechnicalSignal]
    overall_signal: str
    confidence: float
    support_levels: List[float]
    resistance_levels: List[float]
    trend_direction: str
    volatility_score: float
    volume_analysis: Dict[str, Any]

class MarketAnalyzer:
    """
    Advanced market analysis tool with AI-powered insights
    Optimized for Indian stock market patterns
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cache = {}
        self.cache_timestamps = {}
        
    def analyze_symbol(self, symbol: str, timeframe: str = "1h", 
                      lookback_days: int = 30) -> MarketAnalysis:
        """
        Comprehensive technical analysis of a symbol
        
        Args:
            symbol: Stock symbol (e.g., "RELIANCE.NS")
            timeframe: Data timeframe ("1m", "5m", "1h", "1d")
            lookback_days: Days of historical data to analyze
            
        Returns:
            MarketAnalysis object with all signals and insights
        """
        try:
            # Get historical data
            data = self._get_historical_data(symbol, timeframe, lookback_days)
            
            if data.empty:
                self.logger.warning(f"No data available for {symbol}")
                return self._create_empty_analysis(symbol)
            
            # Calculate technical indicators
            data = self._calculate_all_indicators(data)
            
            # Generate technical signals
            technical_signals = self._generate_technical_signals(data)
            
            # Analyze support and resistance
            support_levels, resistance_levels = self._find_support_resistance(data)
            
            # Determine overall trend
            trend_direction = self._analyze_trend(data)
            
            # Calculate volatility
            volatility_score = self._calculate_volatility(data)
            
            # Volume analysis
            volume_analysis = self._analyze_volume(data)
            
            # Combine signals for overall assessment
            overall_signal, confidence = self._combine_signals(technical_signals)
            
            return MarketAnalysis(
                symbol=symbol,
                timestamp=datetime.now(),
                technical_signals=technical_signals,
                overall_signal=overall_signal,
                confidence=confidence,
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                trend_direction=trend_direction,
                volatility_score=volatility_score,
                volume_analysis=volume_analysis
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return self._create_empty_analysis(symbol)
    
    def _get_historical_data(self, symbol: str, timeframe: str, 
                           lookback_days: int) -> pd.DataFrame:
        """Get historical data with caching"""
        cache_key = f"{symbol}_{timeframe}_{lookback_days}"
        
        # Check cache
        if (cache_key in self.cache and 
            cache_key in self.cache_timestamps and
            (datetime.now() - self.cache_timestamps[cache_key]).seconds < 300):
            return self.cache[cache_key]
        
        try:
            ticker = yf.Ticker(symbol)
            period = f"{lookback_days}d"
            data = ticker.history(period=period, interval=timeframe)
            
            # Cache the data
            self.cache[cache_key] = data
            self.cache_timestamps[cache_key] = datetime.now()
            
            return data
            
        except Exception as e:
            self.logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()
    
    def _calculate_all_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """Calculate all technical indicators"""
        try:
            df = data.copy()
            
            # Moving Averages
            df['SMA_20'] = ta.trend.sma_indicator(df['Close'], window=20)
            df['SMA_50'] = ta.trend.sma_indicator(df['Close'], window=50)
            df['EMA_12'] = ta.trend.ema_indicator(df['Close'], window=12)
            df['EMA_26'] = ta.trend.ema_indicator(df['Close'], window=26)
            
            # RSI
            df['RSI'] = ta.momentum.rsi(df['Close'], window=14)
            
            # MACD
            macd = ta.trend.MACD(df['Close'])
            df['MACD'] = macd.macd()
            df['MACD_Signal'] = macd.macd_signal()
            df['MACD_Histogram'] = macd.macd_diff()
            
            # Bollinger Bands
            bollinger = ta.volatility.BollingerBands(df['Close'])
            df['BB_Upper'] = bollinger.bollinger_hband()
            df['BB_Middle'] = bollinger.bollinger_mavg()
            df['BB_Lower'] = bollinger.bollinger_lband()
            df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
            
            # Stochastic Oscillator
            stoch = ta.momentum.StochasticOscillator(df['High'], df['Low'], df['Close'])
            df['Stoch_K'] = stoch.stoch()
            df['Stoch_D'] = stoch.stoch_signal()
            
            # Average True Range (ATR)
            df['ATR'] = ta.volatility.average_true_range(df['High'], df['Low'], df['Close'])
            
            # Volume indicators
            df['Volume_SMA'] = ta.volume.volume_sma(df['Close'], df['Volume'], window=20)
            df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
            
            # On-Balance Volume
            df['OBV'] = ta.volume.on_balance_volume(df['Close'], df['Volume'])
            
            # Commodity Channel Index
            df['CCI'] = ta.trend.cci(df['High'], df['Low'], df['Close'])
            
            # Williams %R
            df['Williams_R'] = ta.momentum.williams_r(df['High'], df['Low'], df['Close'])
            
            # Parabolic SAR
            df['PSAR'] = ta.trend.psar_up(df['High'], df['Low'], df['Close'])
            
            # Ichimoku Cloud
            ichimoku = ta.trend.IchimokuIndicator(df['High'], df['Low'])
            df['Ichimoku_A'] = ichimoku.ichimoku_a()
            df['Ichimoku_B'] = ichimoku.ichimoku_b()
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            return data
    
    def _generate_technical_signals(self, data: pd.DataFrame) -> List[TechnicalSignal]:
        """Generate signals from technical indicators"""
        signals = []
        
        if len(data) < 20:
            return signals
        
        latest = data.iloc[-1]
        prev = data.iloc[-2]
        
        try:
            # RSI Signal
            rsi = latest['RSI']
            if rsi < 30:
                signals.append(TechnicalSignal(
                    "RSI", "BUY", 0.8, rsi, f"RSI oversold at {rsi:.1f}"
                ))
            elif rsi > 70:
                signals.append(TechnicalSignal(
                    "RSI", "SELL", 0.8, rsi, f"RSI overbought at {rsi:.1f}"
                ))
            else:
                signals.append(TechnicalSignal(
                    "RSI", "HOLD", 0.3, rsi, f"RSI neutral at {rsi:.1f}"
                ))
            
            # MACD Signal
            macd = latest['MACD']
            macd_signal = latest['MACD_Signal']
            macd_hist = latest['MACD_Histogram']
            prev_hist = prev['MACD_Histogram']
            
            if macd > macd_signal and macd_hist > prev_hist:
                signals.append(TechnicalSignal(
                    "MACD", "BUY", 0.7, macd_hist, "MACD bullish crossover"
                ))
            elif macd < macd_signal and macd_hist < prev_hist:
                signals.append(TechnicalSignal(
                    "MACD", "SELL", 0.7, macd_hist, "MACD bearish crossover"
                ))
            else:
                signals.append(TechnicalSignal(
                    "MACD", "HOLD", 0.3, macd_hist, "MACD neutral"
                ))
            
            # Moving Average Signal
            price = latest['Close']
            sma_20 = latest['SMA_20']
            sma_50 = latest['SMA_50']
            
            if price > sma_20 > sma_50:
                signals.append(TechnicalSignal(
                    "MA", "BUY", 0.6, price/sma_20, "Price above moving averages"
                ))
            elif price < sma_20 < sma_50:
                signals.append(TechnicalSignal(
                    "MA", "SELL", 0.6, price/sma_20, "Price below moving averages"
                ))
            else:
                signals.append(TechnicalSignal(
                    "MA", "HOLD", 0.3, price/sma_20, "Mixed moving average signals"
                ))
            
            # Bollinger Bands Signal
            bb_upper = latest['BB_Upper']
            bb_lower = latest['BB_Lower']
            bb_middle = latest['BB_Middle']
            
            if price <= bb_lower:
                signals.append(TechnicalSignal(
                    "BB", "BUY", 0.7, (price - bb_lower) / bb_middle, 
                    "Price at lower Bollinger Band"
                ))
            elif price >= bb_upper:
                signals.append(TechnicalSignal(
                    "BB", "SELL", 0.7, (price - bb_upper) / bb_middle,
                    "Price at upper Bollinger Band"
                ))
            else:
                signals.append(TechnicalSignal(
                    "BB", "HOLD", 0.3, (price - bb_middle) / bb_middle,
                    "Price within Bollinger Bands"
                ))
            
            # Stochastic Signal
            stoch_k = latest['Stoch_K']
            stoch_d = latest['Stoch_D']
            
            if stoch_k < 20 and stoch_d < 20:
                signals.append(TechnicalSignal(
                    "STOCH", "BUY", 0.6, stoch_k, "Stochastic oversold"
                ))
            elif stoch_k > 80 and stoch_d > 80:
                signals.append(TechnicalSignal(
                    "STOCH", "SELL", 0.6, stoch_k, "Stochastic overbought"
                ))
            else:
                signals.append(TechnicalSignal(
                    "STOCH", "HOLD", 0.3, stoch_k, "Stochastic neutral"
                ))
            
            # Volume Signal
            volume_ratio = latest['Volume_Ratio']
            
            if volume_ratio > 1.5:
                # High volume - strengthen other signals
                for signal in signals:
                    signal.strength = min(1.0, signal.strength * 1.2)
                
                signals.append(TechnicalSignal(
                    "VOLUME", "CONFIRM", 0.5, volume_ratio,
                    f"High volume confirms signals ({volume_ratio:.1f}x avg)"
                ))
            elif volume_ratio < 0.5:
                signals.append(TechnicalSignal(
                    "VOLUME", "WEAK", 0.3, volume_ratio,
                    f"Low volume weakens signals ({volume_ratio:.1f}x avg)"
                ))
            
        except Exception as e:
            self.logger.error(f"Error generating technical signals: {e}")
        
        return signals
    
    def _find_support_resistance(self, data: pd.DataFrame) -> Tuple[List[float], List[float]]:
        """Find support and resistance levels"""
        try:
            if len(data) < 20:
                return [], []
            
            # Use recent data for S&R calculation
            recent_data = data.tail(50)
            highs = recent_data['High'].values
            lows = recent_data['Low'].values
            
            # Find local maxima and minima
            resistance_levels = []
            support_levels = []
            
            # Simple peak/trough detection
            for i in range(2, len(highs) - 2):
                # Resistance (local maxima)
                if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and 
                    highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                    resistance_levels.append(highs[i])
                
                # Support (local minima)
                if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and 
                    lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                    support_levels.append(lows[i])
            
            # Sort and return top levels
            resistance_levels = sorted(set(resistance_levels), reverse=True)[:3]
            support_levels = sorted(set(support_levels), reverse=True)[:3]
            
            return support_levels, resistance_levels
            
        except Exception as e:
            self.logger.error(f"Error finding support/resistance: {e}")
            return [], []
    
    def _analyze_trend(self, data: pd.DataFrame) -> str:
        """Analyze overall trend direction"""
        try:
            if len(data) < 20:
                return "UNKNOWN"
            
            # Use multiple timeframes for trend analysis
            short_term = data.tail(10)['Close'].mean()
            medium_term = data.tail(20)['Close'].mean()
            long_term = data.tail(50)['Close'].mean() if len(data) >= 50 else medium_term
            
            current_price = data.iloc[-1]['Close']
            
            # Trend scoring
            trend_score = 0
            
            if current_price > short_term:
                trend_score += 1
            if short_term > medium_term:
                trend_score += 1
            if medium_term > long_term:
                trend_score += 1
            
            # Moving average slope
            sma_20 = data['SMA_20'].dropna()
            if len(sma_20) >= 5:
                recent_slope = (sma_20.iloc[-1] - sma_20.iloc[-5]) / sma_20.iloc[-5]
                if recent_slope > 0.01:
                    trend_score += 1
                elif recent_slope < -0.01:
                    trend_score -= 1
            
            if trend_score >= 3:
                return "STRONG_UPTREND"
            elif trend_score >= 1:
                return "UPTREND"
            elif trend_score <= -3:
                return "STRONG_DOWNTREND"
            elif trend_score <= -1:
                return "DOWNTREND"
            else:
                return "SIDEWAYS"
                
        except Exception as e:
            self.logger.error(f"Error analyzing trend: {e}")
            return "UNKNOWN"
    
    def _calculate_volatility(self, data: pd.DataFrame) -> float:
        """Calculate volatility score (0.0 to 1.0)"""
        try:
            if len(data) < 20:
                return 0.5
            
            # Calculate ATR-based volatility
            atr = data['ATR'].iloc[-1]
            current_price = data['Close'].iloc[-1]
            atr_percentage = (atr / current_price) * 100
            
            # Normalize to 0-1 scale (assuming 5% ATR is very high)
            volatility_score = min(1.0, atr_percentage / 5.0)
            
            return volatility_score
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility: {e}")
            return 0.5
    
    def _analyze_volume(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume patterns"""
        try:
            if len(data) < 20:
                return {"status": "insufficient_data"}
            
            recent_volume = data.tail(5)['Volume'].mean()
            avg_volume = data['Volume'].mean()
            volume_ratio = recent_volume / avg_volume
            
            # Volume trend
            volume_trend = "INCREASING" if volume_ratio > 1.2 else "DECREASING" if volume_ratio < 0.8 else "STABLE"
            
            # Price-volume relationship
            recent_price_change = (data.iloc[-1]['Close'] - data.iloc[-5]['Close']) / data.iloc[-5]['Close']
            
            if recent_price_change > 0 and volume_ratio > 1.2:
                pv_relationship = "BULLISH_CONFIRMATION"
            elif recent_price_change < 0 and volume_ratio > 1.2:
                pv_relationship = "BEARISH_CONFIRMATION"
            elif abs(recent_price_change) > 0.02 and volume_ratio < 0.8:
                pv_relationship = "WEAK_MOVE"
            else:
                pv_relationship = "NEUTRAL"
            
            return {
                "volume_ratio": volume_ratio,
                "volume_trend": volume_trend,
                "price_volume_relationship": pv_relationship,
                "recent_volume": recent_volume,
                "average_volume": avg_volume
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing volume: {e}")
            return {"status": "error"}
    
    def _combine_signals(self, signals: List[TechnicalSignal]) -> Tuple[str, float]:
        """Combine all technical signals into overall assessment"""
        try:
            if not signals:
                return "HOLD", 0.0
            
            # Weight signals by strength
            buy_score = 0
            sell_score = 0
            total_weight = 0
            
            for signal in signals:
                weight = signal.strength
                total_weight += weight
                
                if signal.signal == "BUY":
                    buy_score += weight
                elif signal.signal == "SELL":
                    sell_score += weight
            
            if total_weight == 0:
                return "HOLD", 0.0
            
            # Calculate net score
            net_score = (buy_score - sell_score) / total_weight
            confidence = abs(net_score)
            
            # Determine overall signal
            if net_score > 0.3:
                overall_signal = "BUY"
            elif net_score < -0.3:
                overall_signal = "SELL"
            else:
                overall_signal = "HOLD"
            
            return overall_signal, min(1.0, confidence)
            
        except Exception as e:
            self.logger.error(f"Error combining signals: {e}")
            return "HOLD", 0.0
    
    def _create_empty_analysis(self, symbol: str) -> MarketAnalysis:
        """Create empty analysis for error cases"""
        return MarketAnalysis(
            symbol=symbol,
            timestamp=datetime.now(),
            technical_signals=[],
            overall_signal="HOLD",
            confidence=0.0,
            support_levels=[],
            resistance_levels=[],
            trend_direction="UNKNOWN",
            volatility_score=0.5,
            volume_analysis={"status": "no_data"}
        )
    
    async def analyze_sector(self, sector: str) -> Dict[str, MarketAnalysis]:
        """Analyze all stocks in a sector"""
        if sector not in SECTORS:
            self.logger.error(f"Unknown sector: {sector}")
            return {}
        
        stocks = SECTORS[sector]["stocks"]
        analyses = {}
        
        for stock in stocks:
            try:
                analysis = self.analyze_symbol(stock)
                analyses[stock] = analysis
            except Exception as e:
                self.logger.error(f"Error analyzing {stock}: {e}")
        
        return analyses
    
    def get_market_sentiment(self, analyses: Dict[str, MarketAnalysis]) -> Dict[str, Any]:
        """Calculate overall market sentiment from multiple analyses"""
        try:
            if not analyses:
                return {"sentiment": "NEUTRAL", "confidence": 0.0}
            
            buy_signals = 0
            sell_signals = 0
            total_confidence = 0
            
            for analysis in analyses.values():
                if analysis.overall_signal == "BUY":
                    buy_signals += 1
                elif analysis.overall_signal == "SELL":
                    sell_signals += 1
                
                total_confidence += analysis.confidence
            
            total_stocks = len(analyses)
            avg_confidence = total_confidence / total_stocks
            
            # Calculate sentiment
            if buy_signals > sell_signals * 1.5:
                sentiment = "BULLISH"
            elif sell_signals > buy_signals * 1.5:
                sentiment = "BEARISH"
            else:
                sentiment = "NEUTRAL"
            
            return {
                "sentiment": sentiment,
                "confidence": avg_confidence,
                "buy_signals": buy_signals,
                "sell_signals": sell_signals,
                "total_stocks": total_stocks,
                "bullish_percentage": (buy_signals / total_stocks) * 100
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating market sentiment: {e}")
            return {"sentiment": "NEUTRAL", "confidence": 0.0}
