# 🚀 Revolutionary Intraday Trading AI Agent

## The World's Most Advanced Zero-Shot Learning Trading AI

This is a **cutting-edge AI agent** that revolutionizes intraday trading by making **real-time predictions** on live Indian stock market data and **learning from its mistakes** using advanced reinforcement learning techniques.

### 🌟 **Revolutionary Features**

#### 🧠 **Zero-Shot Meta-Learning**
- **No pre-training data required** - learns purely from live market experience
- **Instant adaptation** to new market conditions and sectors
- **Meta-learning architecture** that learns how to learn faster

#### 🎯 **Real-Time Prediction Engine**
- Makes **live predictions** on actual market movements
- **Tracks prediction accuracy** against real outcomes
- **Continuous self-improvement** from every prediction

#### 🏭 **Multi-Sector Adaptability**
- **Specialized strategies** for different industries (IT, Banking, Pharma, etc.)
- **Automatic sector detection** and strategy adaptation
- **Cross-sector learning** and knowledge transfer

#### 🛠️ **Advanced Tool Calling**
- **Technical analysis tools** with 15+ indicators
- **News sentiment analysis** from Indian financial sources
- **Market regime detection** and volatility analysis
- **Risk management** with dynamic position sizing

#### ⚡ **Optimized for Efficiency**
- **4GB RAM compatible** - runs on 5-year-old PCs
- **Memory-efficient neural networks** with quantization
- **Asynchronous processing** for real-time performance
- **Smart caching** and resource management

---

## 🏗️ **Architecture Overview**

```
┌─────────────────────────────────────────────────────────────┐
│                    MAIN ORCHESTRATOR                        │
│                      (main.py)                             │
└─────────────────────┬───────────────────────────────────────┘
                      │
    ┌─────────────────┼─────────────────┐
    │                 │                 │
    ▼                 ▼                 ▼
┌─────────┐    ┌─────────────┐    ┌─────────────┐
│ LIVE    │    │ META-RL     │    │ MARKET      │
│ PRED    │◄──►│ AGENT       │◄──►│ ANALYZER    │
│ ENV     │    │             │    │             │
└─────────┘    └─────────────┘    └─────────────┘
    │                 │                 │
    ▼                 ▼                 ▼
┌─────────┐    ┌─────────────┐    ┌─────────────┐
│ REAL    │    │ NEURAL      │    │ TECHNICAL   │
│ MARKET  │    │ NETWORKS    │    │ INDICATORS  │
│ DATA    │    │             │    │             │
└─────────┘    └─────────────┘    └─────────────┘
```

---

## 🚀 **Quick Start**

### 1. **Installation**

```bash
# Clone the repository
git clone <repository-url>
cd teampas

# Install dependencies
pip install -r requirements.txt
```

### 2. **Run the Demo**

```bash
# See the AI in action with a comprehensive demo
python demo.py
```

### 3. **Start Live Trading**

```bash
# Start with default NIFTY 50 stocks
python main.py

# Focus on specific sector
python main.py --sector IT

# Trade specific symbols
python main.py --symbols RELIANCE.NS TCS.NS HDFCBANK.NS

# Demo mode (safe testing)
python main.py --mode demo
```

---

## 📊 **How It Works**

### **1. Real-Time Prediction Cycle**

```
Market Data → Technical Analysis → AI Prediction → Live Tracking → Learning
     ↑                                                                ↓
     └────────────────── Continuous Improvement ←──────────────────────┘
```

1. **Data Collection**: Fetches real-time data from NSE/BSE via multiple APIs
2. **Technical Analysis**: Applies 15+ indicators (RSI, MACD, Bollinger Bands, etc.)
3. **AI Prediction**: Meta-learning agent makes buy/sell/hold predictions
4. **Live Tracking**: Monitors actual market movements vs predictions
5. **Learning**: Updates neural networks based on prediction accuracy

### **2. Zero-Shot Learning Process**

- **No Historical Training**: Starts with random weights
- **Live Experience**: Learns from each prediction outcome
- **Meta-Learning**: Adapts learning strategy based on performance
- **Sector Adaptation**: Automatically adjusts for different industries

### **3. Advanced Features**

#### **Multi-Source Data Aggregation**
- Primary: Yahoo Finance (yfinance)
- Backup: Alpha Vantage API
- Fallback: Cached historical data
- Real-time: WebSocket connections (when available)

#### **Intelligent Risk Management**
- **Dynamic position sizing** based on confidence levels
- **Sector-specific risk parameters**
- **Volatility-adjusted stop losses**
- **Maximum drawdown protection**

#### **Memory Optimization**
- **Gradient checkpointing** for memory efficiency
- **Mixed precision training** (FP16)
- **Model quantization** for older hardware
- **Smart garbage collection**

---

## 🎯 **Supported Markets & Sectors**

### **Indian Stock Market Focus**
- **NSE (National Stock Exchange)**
- **BSE (Bombay Stock Exchange)**
- **Real-time data** during market hours (9:15 AM - 3:30 PM IST)

### **Supported Sectors**
- 🖥️ **IT**: TCS, Infosys, HCL Tech, Wipro, Tech Mahindra
- 🏦 **Banking**: HDFC Bank, ICICI Bank, SBI, Kotak Bank
- 💊 **Pharma**: Sun Pharma, Cipla, Dr. Reddy's, Divi's Labs
- 🚗 **Auto**: Maruti, Tata Motors, M&M, Eicher Motors
- 🛒 **FMCG**: Hindustan Unilever, ITC, Nestle, Britannia
- ⚡ **Energy**: Reliance, ONGC, BPCL, NTPC
- 🏭 **Metals**: Tata Steel, JSW Steel, Hindalco
- 📱 **Telecom**: Bharti Airtel, Vodafone Idea

---

## 📈 **Performance Metrics**

The AI tracks comprehensive performance metrics:

- **Accuracy Rate**: % of correct predictions
- **Confidence Calibration**: How well confidence matches accuracy
- **Sharpe Ratio**: Risk-adjusted returns
- **Learning Velocity**: Rate of improvement over time
- **Sector Performance**: Best/worst performing sectors
- **Prediction Timing**: Optimal timeframes for predictions

---

## 🔧 **Configuration**

### **System Requirements**
- **Minimum**: 4GB RAM, 2 CPU cores, 1GB storage
- **Recommended**: 8GB RAM, 4 CPU cores, 2GB storage
- **OS**: Windows, Linux, macOS
- **Python**: 3.8+

### **API Keys (Optional)**
```bash
# For enhanced data sources
export ALPHA_VANTAGE_API_KEY="your_key_here"
export NEWS_API_KEY="your_key_here"
```

### **Memory Optimization**
The system automatically detects available memory and optimizes:
- **< 4GB**: Ultra-light mode with minimal models
- **4-8GB**: Standard mode with full features
- **> 8GB**: High-performance mode with larger models

---

## 📊 **Real-Time Dashboard**

The agent provides live updates with:

```
🤖 TRADING AI STATUS UPDATE
================================================================================
⏱️  Session Duration: 2:30:45
🎯 Predictions Made: 127
🎓 Learning Updates: 25

📊 PERFORMANCE METRICS
Total Predictions: 127
Accuracy Rate: 68.5%
Average Confidence: 0.73
Average Reward: +0.142
Learning Velocity: +0.023

🎯 ACTIVE PREDICTIONS
Currently Tracking: 8 predictions

🏆 TOP PERFORMING SYMBOLS
RELIANCE.NS: 75.2% accuracy, +0.189 avg reward
TCS.NS: 71.8% accuracy, +0.156 avg reward
HDFCBANK.NS: 69.4% accuracy, +0.134 avg reward
```

---

## 🧠 **AI Architecture Details**

### **Neural Network Design**
- **Input Layer**: 50 market features
- **Hidden Layers**: [128, 64, 32] neurons (memory optimized)
- **Output Layer**: 5 actions (Strong Buy, Buy, Hold, Sell, Strong Sell)
- **Activation**: ReLU with Batch Normalization
- **Regularization**: Dropout (0.1) + Weight Decay

### **Meta-Learning Components**
- **Task Context Encoding**: Sector, volatility, market regime
- **Adaptation Network**: Fast adaptation to new conditions
- **Attention Mechanism**: Focus on relevant market features
- **Experience Replay**: Efficient learning from past predictions

### **Reinforcement Learning**
- **Algorithm**: Custom Meta-RL with PPO elements
- **Reward Function**: Accuracy + Confidence calibration
- **Exploration**: Epsilon-greedy with adaptive decay
- **Learning Rate**: Adaptive based on performance

---

## 🛡️ **Risk Management**

### **Built-in Safety Features**
- **Paper Trading Only**: No real money at risk
- **Position Limits**: Maximum exposure per stock/sector
- **Drawdown Protection**: Automatic risk reduction
- **Volatility Adjustment**: Dynamic risk based on market conditions

### **Prediction Validation**
- **Confidence Thresholds**: Only act on high-confidence predictions
- **Cross-Validation**: Multiple indicators must align
- **Sector Consistency**: Check against sector trends
- **Market Regime**: Adapt strategy to current market conditions

---

## 🔮 **Future Enhancements**

- **Options Trading**: Extend to derivatives
- **Global Markets**: Support for US, European markets
- **Crypto Integration**: Cryptocurrency trading
- **News Integration**: Real-time news sentiment
- **Social Sentiment**: Twitter/Reddit sentiment analysis
- **Portfolio Optimization**: Multi-asset allocation

---

## 📞 **Support & Contributing**

This is a revolutionary AI system that pushes the boundaries of what's possible in algorithmic trading. The zero-shot learning approach means it gets smarter with every prediction!

### **Key Innovation**: 
Unlike traditional trading bots that require extensive backtesting and historical data, this agent learns in real-time from live market conditions, making it incredibly adaptive and robust.

---

## ⚠️ **Disclaimer**

This AI agent is for **educational and research purposes only**. It operates in **paper trading mode** and does not execute real trades. Always consult with financial advisors before making investment decisions. Past performance does not guarantee future results.

---

**🚀 Ready to witness the future of AI trading? Run `python demo.py` to see the magic!**
