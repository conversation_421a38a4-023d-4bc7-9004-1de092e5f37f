PS E:\exprimental\teampas> python demo_visualization.py
================================================================================
🚀 COMPLETE TRADING VISUALIZATION DEMO
================================================================================
This demo showcases:
✅ Real stock price visualization with predictions
✅ Portfolio management with ₹1000 budget
✅ P&L tracking and performance metrics
✅ Live trading simulation
✅ Interactive charts and dashboards
================================================================================
================================================================================
💰 PORTFOLIO MANAGEMENT SYSTEM DEMO
Starting with ₹1000 budget
================================================================================

📊 Initial Portfolio State
Initial Capital: ₹1,000.00
Cash Balance: ₹1,000.00
Total Value: ₹1,000.00
Total Trades: 0

🎯 Simulating AI Trading Decisions...

📊 Processing RELIANCE.NS...
Current Price: ₹1417.50
AI Prediction: HOLD (Confidence: 86.4%)

📊 Processing TCS.NS...
Current Price: ₹3463.40
AI Prediction: BUY (Confidence: 89.0%)
Buy Check: Cannot afford even 1 share at ₹3463.40

📊 Processing HDFCBANK.NS...
Current Price: ₹1941.40
AI Prediction: HOLD (Confidence: 85.1%)

📊 Final Portfolio State
Cash Balance: ₹1,000.00
Positions Value: ₹0.00
Total Value: ₹1,000.00
Total P&L: ₹+0.00
Return: +0.00%
Total Trades: 0
Win Rate: 0.0%

================================================================================
📊 VISUALIZATION SYSTEM DEMO
================================================================================

📈 Creating Stock Charts...
Creating chart for RELIANCE.NS...
INFO:data.market_data_fetcher:Loaded 743 data points for RELIANCE.NS
INFO:visualization.trading_visualizer:📊 Stock chart created for RELIANCE.NS: visualization\charts\RELIANCE.NS_chart_20250601_215944.html
✅ Chart created for RELIANCE.NS
Creating chart for TCS.NS...
INFO:data.market_data_fetcher:Loaded 744 data points for TCS.NS
INFO:visualization.trading_visualizer:📊 Stock chart created for TCS.NS: visualization\charts\TCS.NS_chart_20250601_215947.html
✅ Chart created for TCS.NS

💰 Creating Portfolio Performance Chart...
INFO:visualization.trading_visualizer:📊 Portfolio performance chart created: visualization\charts\portfolio_performance_20250601_215947.html
✅ Portfolio performance chart created

🎛️  Creating Live Dashboard...
ERROR:visualization.trading_visualizer:Error updating dashboard data: 'coroutine' object has no attribute 'volume'
E:\exprimental\teampas\visualization\trading_visualizer.py:77: RuntimeWarning:

coroutine 'MarketDataFetcher.fetch_realtime_data' was never awaited

INFO:visualization.trading_visualizer:📊 Live dashboard created: visualization\charts\live_dashboard_20250601_215947.html
✅ Live dashboard created

📋 Creating Daily Summary...
INFO:visualization.trading_visualizer:📊 Daily summary chart saved: visualization\charts\daily_summary_20250601.png
✅ Daily summary saved: visualization\charts\daily_summary_20250601.png

📁 Visualization Files Created
📊 daily_summary_20250601.png
📊 live_dashboard_20250601_215947.html
📊 portfolio_performance_20250601_215947.html
📊 RELIANCE.NS_chart_20250601_215944.html
📊 TCS.NS_chart_20250601_215947.html

================================================================================
🚀 LIVE TRADING SIMULATION
Real-time predictions with portfolio execution
================================================================================

🎯 Starting Live Prediction Environment...
INFO:environment.live_prediction_env:🚀 Starting Live Prediction Environment
INFO:environment.live_prediction_env:✅ Live environment started successfully

📊 Making prediction 1 for RELIANCE.NS...
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
INFO:environment.live_prediction_env:🎯 NEW PREDICTION: RELIANCE.NS | STRONG_SELL | Confidence: 0.88 | Expected: +1.33% | Timeframe: 2min
✅ Prediction made: STRONG_SELL (confidence: 87.5%)

📊 Making prediction 1 for TCS.NS...
INFO:environment.live_prediction_env:🎯 NEW PREDICTION: TCS.NS | HOLD | Confidence: 0.60 | Expected: -1.87% | Timeframe: 2min
✅ Prediction made: HOLD (confidence: 59.6%)
⏳ Waiting 30 seconds for predictions to resolve...
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'

📊 Making prediction 2 for RELIANCE.NS...
INFO:environment.live_prediction_env:🎯 NEW PREDICTION: RELIANCE.NS | SELL | Confidence: 0.56 | Expected: -1.62% | Timeframe: 2min
✅ Prediction made: SELL (confidence: 55.5%)

object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'

📊 Making prediction 3 for RELIANCE.NS...
INFO:environment.live_prediction_env:🎯 NEW PREDICTION: RELIANCE.NS | STRONG_SELL | Confidence: 0.67 | Expected: -1.31% | Timeframe: 2min     
✅ Prediction made: STRONG_SELL (confidence: 67.3%)

📊 Making prediction 3 for TCS.NS...
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
INFO:environment.live_prediction_env:🎯 NEW PREDICTION: TCS.NS | BUY | Confidence: 0.71 | Expected: +1.50% | Timeframe: 2min
✅ Prediction made: BUY (confidence: 71.2%)
⏳ Waiting 30 seconds for predictions to resolve...
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'

⏳ Waiting for final predictions to resolve...
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
WARNING:environment.live_prediction_env:No current data for RELIANCE.NS
WARNING:environment.live_prediction_env:No current data for TCS.NS
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
WARNING:environment.live_prediction_env:No current data for RELIANCE.NS
WARNING:environment.live_prediction_env:No current data for TCS.NS
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
WARNING:environment.live_prediction_env:No current data for RELIANCE.NS
WARNING:environment.live_prediction_env:No current data for TCS.NS
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'
ERROR:environment.live_prediction_env:Data collection error: 'NoneType' object has no attribute 'fetch_realtime_data'

📊 Final Trading Results

💰 Portfolio Performance:
Initial Capital: ₹1,000.00
Final Value: ₹1,000.00
Total P&L: ₹+0.00
Return: +0.00%

🎯 Prediction Performance:
Total Predictions: 0
Accuracy Rate: 0.0%
Average Confidence: 0.00
Average Reward: +0.000
INFO:environment.live_prediction_env:🛑 Stopping Live Prediction Environment

================================================================================
🎉 DEMO COMPLETED SUCCESSFULLY!
================================================================================

📊 Files Created:
📁 portfolio/ - Portfolio database and trade records
📁 visualization/charts/ - Interactive charts and dashboards
📁 memory/ - AI learning experiences and patterns
📁 reports/ - Comprehensive analysis reports

🎯 Key Features Demonstrated:
✅ Real-time stock price tracking
✅ AI prediction visualization
✅ Portfolio management with ₹1000 budget
✅ Automatic trade execution
✅ P&L tracking and performance analysis
✅ Interactive charts and dashboards
✅ Memory-enhanced decision making

💡 The AI agent now has:
🧠 Persistent memory that learns from every trade
💰 Real portfolio management with budget tracking
📊 Beautiful visualizations of performance
🎯 Live prediction and execution capabilities
📈 Comprehensive P&L and performance metrics

💰 FINAL PORTFOLIO STATE:
💵 Total Value: ₹1,000.00
📈 Total Return: +0.00%
🎯 Total Trades: 0
🏆 Win Rate: 0.0%

Ready to start live trading with visualization!
Run: python start_memory_agent.py
PS E:\exprimental\teampas> 