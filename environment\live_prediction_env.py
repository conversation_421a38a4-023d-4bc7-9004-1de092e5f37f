"""
Live Market Prediction Environment
Revolutionary approach: Agent makes real predictions and learns from actual market outcomes
"""

import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging
from dataclasses import dataclass, asdict
from enum import Enum
import json
import sqlite3
from collections import deque
import threading
import time

from data.market_data_fetcher import market_data_fetcher, MarketData
from config.indian_stocks import NIFTY_50, SECTORS
from config.settings import settings
from portfolio.portfolio_manager import portfolio_manager

class PredictionAction(Enum):
    STRONG_BUY = 2
    BUY = 1
    HOLD = 0
    SELL = -1
    STRONG_SELL = -2

@dataclass
class Prediction:
    """A single prediction made by the agent"""
    id: str
    timestamp: datetime
    symbol: str
    action: PredictionAction
    confidence: float  # 0.0 to 1.0
    predicted_price_change: float  # Expected % change
    timeframe: int  # Minutes ahead
    market_data_snapshot: Dict[str, Any]
    reasoning: str  # AI's reasoning for the prediction
    
    # Outcome tracking (filled later)
    actual_price_change: Optional[float] = None
    outcome_timestamp: Optional[datetime] = None
    accuracy_score: Optional[float] = None
    reward: Optional[float] = None
    is_resolved: bool = False

@dataclass
class LearningMetrics:
    """Metrics for tracking agent's learning progress"""
    total_predictions: int = 0
    correct_predictions: int = 0
    accuracy_rate: float = 0.0
    average_confidence: float = 0.0
    average_reward: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    learning_velocity: float = 0.0  # Rate of improvement

class LivePredictionEnvironment:
    """
    Revolutionary trading environment where agent makes real predictions
    and learns from actual market outcomes
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Prediction tracking
        self.active_predictions: Dict[str, Prediction] = {}
        self.resolved_predictions: List[Prediction] = []
        self.prediction_history = deque(maxlen=10000)  # Memory efficient
        
        # Learning metrics
        self.metrics = LearningMetrics()
        self.performance_history = deque(maxlen=1000)
        
        # Real-time data
        self.current_market_data: Dict[str, MarketData] = {}
        self.market_state_history = deque(maxlen=100)
        
        # Database for persistence
        self.db_path = "data/predictions.db"
        self._init_database()
        
        # Threading for real-time operations
        self.is_running = False
        self.data_thread = None
        self.evaluation_thread = None
        
        # Learning state
        self.learning_buffer = deque(maxlen=1000)
        self.last_learning_update = datetime.now()
        
    def _init_database(self):
        """Initialize SQLite database for prediction storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS predictions (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT,
                    symbol TEXT,
                    action INTEGER,
                    confidence REAL,
                    predicted_price_change REAL,
                    timeframe INTEGER,
                    market_data_snapshot TEXT,
                    reasoning TEXT,
                    actual_price_change REAL,
                    outcome_timestamp TEXT,
                    accuracy_score REAL,
                    reward REAL,
                    is_resolved BOOLEAN
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learning_metrics (
                    timestamp TEXT PRIMARY KEY,
                    total_predictions INTEGER,
                    correct_predictions INTEGER,
                    accuracy_rate REAL,
                    average_confidence REAL,
                    average_reward REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    learning_velocity REAL
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Database initialization error: {e}")
    
    async def start_live_environment(self):
        """Start the live prediction environment"""
        self.logger.info("🚀 Starting Live Prediction Environment")
        self.is_running = True
        
        # Start real-time data collection
        self.data_thread = threading.Thread(target=self._run_data_collection)
        self.data_thread.daemon = True
        self.data_thread.start()
        
        # Start prediction evaluation
        self.evaluation_thread = threading.Thread(target=self._run_prediction_evaluation)
        self.evaluation_thread.daemon = True
        self.evaluation_thread.start()
        
        self.logger.info("✅ Live environment started successfully")
    
    def _run_data_collection(self):
        """Continuously collect real-time market data"""
        while self.is_running:
            try:
                # Collect data for all tracked symbols
                symbols = NIFTY_50[:10]  # Start with top 10 for efficiency
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                tasks = [market_data_fetcher.fetch_realtime_data(symbol) for symbol in symbols]
                results = loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))
                
                # Update current market data
                for symbol, result in zip(symbols, results):
                    if isinstance(result, MarketData):
                        self.current_market_data[symbol] = result
                
                # Store market state snapshot
                market_snapshot = {
                    'timestamp': datetime.now(),
                    'data': {k: asdict(v) for k, v in self.current_market_data.items()}
                }
                self.market_state_history.append(market_snapshot)
                
                loop.close()
                time.sleep(settings.data.update_frequency)
                
            except Exception as e:
                self.logger.error(f"Data collection error: {e}")
                time.sleep(5)
    
    def _run_prediction_evaluation(self):
        """Continuously evaluate active predictions"""
        while self.is_running:
            try:
                current_time = datetime.now()
                predictions_to_resolve = []
                
                # Check which predictions should be resolved
                for pred_id, prediction in self.active_predictions.items():
                    time_elapsed = (current_time - prediction.timestamp).total_seconds() / 60
                    
                    if time_elapsed >= prediction.timeframe:
                        predictions_to_resolve.append(pred_id)
                
                # Resolve predictions
                for pred_id in predictions_to_resolve:
                    self._resolve_prediction(pred_id)
                
                # Update learning metrics
                self._update_learning_metrics()
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Prediction evaluation error: {e}")
                time.sleep(5)
    
    def make_prediction(self, symbol: str, action: PredictionAction, 
                       confidence: float, predicted_change: float,
                       timeframe: int = 5, reasoning: str = "") -> str:
        """
        Agent makes a prediction about market movement
        
        Args:
            symbol: Stock symbol
            action: Predicted action (buy/sell/hold)
            confidence: Confidence level (0.0 to 1.0)
            predicted_change: Expected price change %
            timeframe: Minutes ahead for prediction
            reasoning: AI's reasoning
            
        Returns:
            Prediction ID for tracking
        """
        try:
            # Generate unique prediction ID
            pred_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Get current market snapshot
            market_snapshot = {
                'current_price': self.current_market_data.get(symbol),
                'market_indices': {k: asdict(v) for k, v in self.current_market_data.items() 
                                 if k in ['^NSEI', '^BSESN']},
                'timestamp': datetime.now().isoformat()
            }
            
            # Create prediction
            prediction = Prediction(
                id=pred_id,
                timestamp=datetime.now(),
                symbol=symbol,
                action=action,
                confidence=confidence,
                predicted_price_change=predicted_change,
                timeframe=timeframe,
                market_data_snapshot=market_snapshot,
                reasoning=reasoning
            )
            
            # Store active prediction
            self.active_predictions[pred_id] = prediction
            
            # Log the prediction
            self.logger.info(f"🎯 NEW PREDICTION: {symbol} | {action.name} | "
                           f"Confidence: {confidence:.2f} | "
                           f"Expected: {predicted_change:+.2f}% | "
                           f"Timeframe: {timeframe}min")
            
            # Save to database
            self._save_prediction_to_db(prediction)
            
            return pred_id
            
        except Exception as e:
            self.logger.error(f"Error making prediction: {e}")
            return ""
    
    def _resolve_prediction(self, pred_id: str):
        """Resolve a prediction by comparing with actual market outcome"""
        try:
            if pred_id not in self.active_predictions:
                return
            
            prediction = self.active_predictions[pred_id]
            current_time = datetime.now()
            
            # Get current market data for the symbol
            current_data = self.current_market_data.get(prediction.symbol)
            if not current_data:
                self.logger.warning(f"No current data for {prediction.symbol}")
                return
            
            # Calculate actual price change
            original_snapshot = prediction.market_data_snapshot
            if 'current_price' in original_snapshot and original_snapshot['current_price']:
                original_price = original_snapshot['current_price']['close']
                current_price = current_data.close
                actual_change = ((current_price - original_price) / original_price) * 100
            else:
                self.logger.warning(f"No original price data for prediction {pred_id}")
                return
            
            # Calculate accuracy and reward
            accuracy_score = self._calculate_accuracy(prediction, actual_change)
            reward = self._calculate_reward(prediction, actual_change, accuracy_score)
            
            # Update prediction
            prediction.actual_price_change = actual_change
            prediction.outcome_timestamp = current_time
            prediction.accuracy_score = accuracy_score
            prediction.reward = reward
            prediction.is_resolved = True

            # Execute portfolio trade based on prediction outcome
            self._execute_portfolio_trade(prediction, current_data)

            # Move to resolved predictions
            self.resolved_predictions.append(prediction)
            self.prediction_history.append(prediction)
            self.learning_buffer.append(prediction)
            del self.active_predictions[pred_id]

            # Log the outcome
            self.logger.info(f"📊 RESOLVED: {prediction.symbol} | "
                           f"Predicted: {prediction.predicted_price_change:+.2f}% | "
                           f"Actual: {actual_change:+.2f}% | "
                           f"Accuracy: {accuracy_score:.2f} | "
                           f"Reward: {reward:+.3f}")

            # Update database
            self._update_prediction_in_db(prediction)
            
        except Exception as e:
            self.logger.error(f"Error resolving prediction {pred_id}: {e}")

    def _execute_portfolio_trade(self, prediction: Prediction, current_data: MarketData):
        """Execute actual portfolio trade based on prediction outcome"""
        try:
            symbol = prediction.symbol
            current_price = current_data.close
            accuracy = prediction.accuracy_score
            confidence = prediction.confidence

            # Only execute trades for high-accuracy predictions
            if accuracy < 0.6:
                self.logger.info(f"⏭️  Skipping trade for {symbol} - low accuracy ({accuracy:.1%})")
                return

            # Determine trade action based on original prediction and outcome
            original_action = prediction.action

            if original_action in [PredictionAction.BUY, PredictionAction.STRONG_BUY]:
                if accuracy > 0.7:  # Good prediction, execute buy
                    trade = portfolio_manager.execute_buy(
                        symbol=symbol,
                        price=current_price,
                        confidence=confidence,
                        reasoning=f"AI prediction confirmed: {prediction.reasoning}"
                    )
                    if trade:
                        self.logger.info(f"💰 EXECUTED BUY: {trade.quantity} {symbol} @ ₹{trade.price:.2f}")

            elif original_action in [PredictionAction.SELL, PredictionAction.STRONG_SELL]:
                if accuracy > 0.7:  # Good prediction, execute sell
                    trade = portfolio_manager.execute_sell(
                        symbol=symbol,
                        price=current_price,
                        confidence=confidence,
                        reasoning=f"AI prediction confirmed: {prediction.reasoning}"
                    )
                    if trade:
                        self.logger.info(f"💰 EXECUTED SELL: {trade.quantity} {symbol} @ ₹{trade.price:.2f}")

            # Update portfolio positions with current prices
            current_prices = {symbol: current_price}
            portfolio_manager.update_positions(current_prices)

            # Save portfolio snapshot
            portfolio_manager.save_portfolio_snapshot()

        except Exception as e:
            self.logger.error(f"Error executing portfolio trade: {e}")

    def _calculate_accuracy(self, prediction: Prediction, actual_change: float) -> float:
        """Calculate prediction accuracy score"""
        try:
            # Direction accuracy (most important)
            predicted_direction = 1 if prediction.predicted_price_change > 0 else -1
            actual_direction = 1 if actual_change > 0 else -1
            direction_correct = predicted_direction == actual_direction
            
            # Magnitude accuracy
            magnitude_error = abs(prediction.predicted_price_change - actual_change)
            magnitude_accuracy = max(0, 1 - (magnitude_error / 10))  # Normalize to 0-1
            
            # Combined accuracy weighted by confidence
            if direction_correct:
                base_accuracy = 0.7 + (0.3 * magnitude_accuracy)
            else:
                base_accuracy = 0.3 * magnitude_accuracy
            
            # Adjust by confidence (higher confidence predictions should be more accurate)
            confidence_adjustment = prediction.confidence
            final_accuracy = base_accuracy * confidence_adjustment
            
            return min(1.0, max(0.0, final_accuracy))
            
        except Exception as e:
            self.logger.error(f"Error calculating accuracy: {e}")
            return 0.0
    
    def _calculate_reward(self, prediction: Prediction, actual_change: float, 
                         accuracy_score: float) -> float:
        """Calculate reward for reinforcement learning"""
        try:
            # Base reward from accuracy
            base_reward = (accuracy_score - 0.5) * 2  # Scale to -1 to +1
            
            # Bonus for high confidence correct predictions
            confidence_bonus = 0
            if accuracy_score > 0.7 and prediction.confidence > 0.8:
                confidence_bonus = 0.2
            
            # Penalty for overconfident wrong predictions
            confidence_penalty = 0
            if accuracy_score < 0.3 and prediction.confidence > 0.8:
                confidence_penalty = -0.3
            
            # Magnitude bonus (reward larger correct predictions more)
            magnitude_bonus = 0
            if accuracy_score > 0.6:
                magnitude_bonus = min(0.2, abs(actual_change) / 10)
            
            total_reward = base_reward + confidence_bonus + confidence_penalty + magnitude_bonus
            
            return max(-1.0, min(1.0, total_reward))
            
        except Exception as e:
            self.logger.error(f"Error calculating reward: {e}")
            return 0.0
    
    def get_learning_experience(self) -> List[Dict[str, Any]]:
        """Get recent learning experiences for RL training"""
        experiences = []
        
        for prediction in list(self.learning_buffer):
            if prediction.is_resolved:
                experience = {
                    'state': prediction.market_data_snapshot,
                    'action': prediction.action.value,
                    'reward': prediction.reward,
                    'next_state': self._get_outcome_state(prediction),
                    'done': True,
                    'info': {
                        'accuracy': prediction.accuracy_score,
                        'confidence': prediction.confidence,
                        'symbol': prediction.symbol,
                        'reasoning': prediction.reasoning
                    }
                }
                experiences.append(experience)
        
        # Clear the buffer after extracting experiences
        self.learning_buffer.clear()
        
        return experiences
    
    def _get_outcome_state(self, prediction: Prediction) -> Dict[str, Any]:
        """Get the market state when prediction was resolved"""
        # Find the closest market state to the outcome timestamp
        if not prediction.outcome_timestamp:
            return {}
        
        closest_state = None
        min_time_diff = float('inf')
        
        for state in self.market_state_history:
            time_diff = abs((state['timestamp'] - prediction.outcome_timestamp).total_seconds())
            if time_diff < min_time_diff:
                min_time_diff = time_diff
                closest_state = state
        
        return closest_state['data'] if closest_state else {}
    
    def _update_learning_metrics(self):
        """Update learning performance metrics"""
        try:
            if not self.resolved_predictions:
                return
            
            # Calculate metrics from resolved predictions
            total = len(self.resolved_predictions)
            correct = sum(1 for p in self.resolved_predictions if p.accuracy_score > 0.6)
            
            self.metrics.total_predictions = total
            self.metrics.correct_predictions = correct
            self.metrics.accuracy_rate = correct / total if total > 0 else 0.0
            
            # Average metrics
            self.metrics.average_confidence = np.mean([p.confidence for p in self.resolved_predictions])
            self.metrics.average_reward = np.mean([p.reward for p in self.resolved_predictions])
            
            # Calculate Sharpe ratio (risk-adjusted returns)
            rewards = [p.reward for p in self.resolved_predictions]
            if len(rewards) > 1:
                self.metrics.sharpe_ratio = np.mean(rewards) / (np.std(rewards) + 1e-8)
            
            # Learning velocity (improvement rate)
            if len(self.resolved_predictions) >= 20:
                recent_accuracy = np.mean([p.accuracy_score for p in self.resolved_predictions[-10:]])
                older_accuracy = np.mean([p.accuracy_score for p in self.resolved_predictions[-20:-10]])
                self.metrics.learning_velocity = recent_accuracy - older_accuracy
            
            # Store metrics history
            self.performance_history.append({
                'timestamp': datetime.now(),
                'metrics': asdict(self.metrics)
            })
            
            # Save to database
            self._save_metrics_to_db()
            
        except Exception as e:
            self.logger.error(f"Error updating learning metrics: {e}")
    
    def _save_prediction_to_db(self, prediction: Prediction):
        """Save prediction to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO predictions VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                prediction.id,
                prediction.timestamp.isoformat(),
                prediction.symbol,
                prediction.action.value,
                prediction.confidence,
                prediction.predicted_price_change,
                prediction.timeframe,
                json.dumps(prediction.market_data_snapshot),
                prediction.reasoning,
                prediction.actual_price_change,
                prediction.outcome_timestamp.isoformat() if prediction.outcome_timestamp else None,
                prediction.accuracy_score,
                prediction.reward,
                prediction.is_resolved
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error saving prediction to database: {e}")
    
    def _update_prediction_in_db(self, prediction: Prediction):
        """Update resolved prediction in database"""
        self._save_prediction_to_db(prediction)  # Same method works for updates
    
    def _save_metrics_to_db(self):
        """Save learning metrics to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO learning_metrics VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                datetime.now().isoformat(),
                self.metrics.total_predictions,
                self.metrics.correct_predictions,
                self.metrics.accuracy_rate,
                self.metrics.average_confidence,
                self.metrics.average_reward,
                self.metrics.sharpe_ratio,
                self.metrics.max_drawdown,
                self.metrics.learning_velocity
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error saving metrics to database: {e}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        return {
            'metrics': asdict(self.metrics),
            'active_predictions': len(self.active_predictions),
            'resolved_predictions': len(self.resolved_predictions),
            'recent_performance': list(self.performance_history)[-10:] if self.performance_history else [],
            'top_performing_symbols': self._get_top_performing_symbols(),
            'learning_insights': self._get_learning_insights()
        }
    
    def _get_top_performing_symbols(self) -> List[Dict[str, Any]]:
        """Get symbols where agent performs best"""
        symbol_performance = {}
        
        for prediction in self.resolved_predictions:
            symbol = prediction.symbol
            if symbol not in symbol_performance:
                symbol_performance[symbol] = {'total': 0, 'correct': 0, 'avg_reward': 0}
            
            symbol_performance[symbol]['total'] += 1
            if prediction.accuracy_score > 0.6:
                symbol_performance[symbol]['correct'] += 1
            symbol_performance[symbol]['avg_reward'] += prediction.reward
        
        # Calculate accuracy and sort
        for symbol in symbol_performance:
            perf = symbol_performance[symbol]
            perf['accuracy'] = perf['correct'] / perf['total']
            perf['avg_reward'] /= perf['total']
        
        return sorted(symbol_performance.items(), 
                     key=lambda x: x[1]['accuracy'], reverse=True)[:5]
    
    def _get_learning_insights(self) -> Dict[str, Any]:
        """Generate insights about agent's learning progress"""
        if len(self.resolved_predictions) < 10:
            return {"message": "Need more predictions for insights"}
        
        insights = {}
        
        # Confidence calibration
        high_conf_predictions = [p for p in self.resolved_predictions if p.confidence > 0.8]
        if high_conf_predictions:
            high_conf_accuracy = np.mean([p.accuracy_score for p in high_conf_predictions])
            insights['confidence_calibration'] = {
                'high_confidence_accuracy': high_conf_accuracy,
                'is_well_calibrated': high_conf_accuracy > 0.7
            }
        
        # Learning trend
        if len(self.resolved_predictions) >= 20:
            recent_rewards = [p.reward for p in self.resolved_predictions[-10:]]
            older_rewards = [p.reward for p in self.resolved_predictions[-20:-10]]
            
            insights['learning_trend'] = {
                'recent_avg_reward': np.mean(recent_rewards),
                'older_avg_reward': np.mean(older_rewards),
                'improvement': np.mean(recent_rewards) - np.mean(older_rewards)
            }
        
        return insights
    
    def stop(self):
        """Stop the live environment"""
        self.logger.info("🛑 Stopping Live Prediction Environment")
        self.is_running = False
        
        if self.data_thread:
            self.data_thread.join(timeout=5)
        if self.evaluation_thread:
            self.evaluation_thread.join(timeout=5)

# Global instance
live_env = LivePredictionEnvironment()
