"""
Advanced Trading AI Agent Configuration
Optimized for 4GB RAM and cutting-edge performance
"""

import os
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

class TradingMode(Enum):
    PAPER = "paper"
    LIVE = "live"
    BACKTEST = "backtest"

class RLAlgorithm(Enum):
    PPO = "ppo"
    SAC = "sac"
    A3C = "a3c"
    ENSEMBLE = "ensemble"
    META_LEARNING = "meta_learning"

@dataclass
class ModelConfig:
    """Memory-optimized model configuration for old PCs"""
    # Neural network architecture (optimized for 4GB RAM)
    hidden_layers: List[int] = None
    activation: str = "relu"
    dropout_rate: float = 0.1
    use_batch_norm: bool = True
    
    # Memory optimization
    gradient_checkpointing: bool = True
    mixed_precision: bool = True
    model_compression: bool = True
    quantization: bool = True
    
    # Training parameters
    batch_size: int = 32  # Small batch for memory efficiency
    learning_rate: float = 3e-4
    max_grad_norm: float = 0.5
    
    def __post_init__(self):
        if self.hidden_layers is None:
            # Compact architecture for efficiency
            self.hidden_layers = [128, 64, 32]

@dataclass
class TradingConfig:
    """Trading strategy configuration"""
    mode: TradingMode = TradingMode.PAPER
    initial_capital: float = 100000.0
    max_positions: int = 5
    risk_per_trade: float = 0.02
    commission: float = 0.0003
    slippage: float = 0.0001
    
    # Position sizing
    position_sizing_method: str = "kelly"  # kelly, fixed, volatility
    max_position_size: float = 0.2
    min_position_size: float = 0.01
    
    # Risk management
    max_drawdown: float = 0.15
    daily_loss_limit: float = 0.05
    stop_loss_method: str = "adaptive"  # fixed, adaptive, trailing
    take_profit_method: str = "dynamic"  # fixed, dynamic, trailing

@dataclass
class DataConfig:
    """Market data configuration"""
    # Data sources
    primary_source: str = "yfinance"
    backup_sources: List[str] = None
    
    # Data frequency
    timeframes: List[str] = None
    lookback_days: int = 30
    
    # Real-time data
    enable_realtime: bool = True
    update_frequency: int = 1  # seconds
    
    # Data storage
    cache_data: bool = True
    cache_duration: int = 3600  # seconds
    
    def __post_init__(self):
        if self.backup_sources is None:
            self.backup_sources = ["alpha_vantage", "stockdex"]
        if self.timeframes is None:
            self.timeframes = ["1m", "5m", "15m", "1h", "1d"]

@dataclass
class RLConfig:
    """Reinforcement Learning configuration"""
    algorithm: RLAlgorithm = RLAlgorithm.META_LEARNING
    
    # Environment
    observation_space_size: int = 50
    action_space_size: int = 3  # buy, sell, hold
    reward_function: str = "sharpe_ratio"  # profit, sharpe_ratio, sortino_ratio
    
    # Training
    total_timesteps: int = 100000
    eval_freq: int = 1000
    save_freq: int = 5000
    
    # Meta-learning parameters
    meta_learning_rate: float = 1e-3
    adaptation_steps: int = 5
    meta_batch_size: int = 4
    
    # Zero-shot learning
    enable_zero_shot: bool = True
    functional_reward_encoding: bool = True
    
    # Memory efficiency
    replay_buffer_size: int = 10000  # Reduced for memory efficiency
    n_envs: int = 1  # Single environment for memory conservation

@dataclass
class OptimizationConfig:
    """Performance optimization for old hardware"""
    # CPU optimization
    num_threads: int = 2  # Conservative for old PCs
    use_multiprocessing: bool = False  # Avoid memory overhead
    
    # Memory management
    memory_limit_mb: int = 3000  # Leave 1GB for OS
    garbage_collection_freq: int = 100
    clear_cache_freq: int = 1000
    
    # Model optimization
    use_tensorrt: bool = False  # Too heavy for old PCs
    use_onnx: bool = True
    model_pruning: bool = True
    weight_quantization: bool = True
    
    # Monitoring
    enable_profiling: bool = True
    log_memory_usage: bool = True

@dataclass
class ToolsConfig:
    """AI tools configuration"""
    # Market analysis tools
    enable_technical_analysis: bool = True
    enable_sentiment_analysis: bool = True
    enable_news_analysis: bool = True
    enable_sector_analysis: bool = True
    
    # Tool calling parameters
    max_tool_calls_per_decision: int = 3
    tool_timeout: float = 5.0
    
    # News and sentiment
    news_sources_limit: int = 5
    sentiment_analysis_method: str = "vader"  # vader, textblob, custom
    
    # Technical indicators
    default_indicators: List[str] = None
    
    def __post_init__(self):
        if self.default_indicators is None:
            self.default_indicators = [
                "sma_20", "ema_12", "rsi_14", "macd", "bollinger_bands",
                "stochastic", "atr", "volume_sma", "vwap"
            ]

class Settings:
    """Main settings class"""
    
    def __init__(self):
        self.model = ModelConfig()
        self.trading = TradingConfig()
        self.data = DataConfig()
        self.rl = RLConfig()
        self.optimization = OptimizationConfig()
        self.tools = ToolsConfig()
        
        # Environment variables
        self.alpha_vantage_api_key = os.getenv("ALPHA_VANTAGE_API_KEY")
        self.news_api_key = os.getenv("NEWS_API_KEY")
        
        # Paths
        self.data_dir = "data"
        self.models_dir = "models"
        self.logs_dir = "logs"
        self.cache_dir = "cache"
        
        # Create directories
        self._create_directories()
    
    def _create_directories(self):
        """Create necessary directories"""
        for dir_path in [self.data_dir, self.models_dir, self.logs_dir, self.cache_dir]:
            os.makedirs(dir_path, exist_ok=True)
    
    def get_memory_optimized_config(self) -> Dict[str, Any]:
        """Get configuration optimized for low memory systems"""
        return {
            "model": {
                "hidden_layers": [64, 32],  # Even smaller
                "batch_size": 16,
                "gradient_checkpointing": True,
                "mixed_precision": True
            },
            "rl": {
                "replay_buffer_size": 5000,
                "n_envs": 1
            },
            "optimization": {
                "memory_limit_mb": 2500,
                "num_threads": 1
            }
        }
    
    def adapt_for_sector(self, sector: str) -> Dict[str, Any]:
        """Adapt configuration for specific sector"""
        from .indian_stocks import SECTOR_TRADING_PARAMS
        
        if sector in SECTOR_TRADING_PARAMS:
            params = SECTOR_TRADING_PARAMS[sector]
            return {
                "trading": {
                    "max_position_size": params["max_position_size"],
                    "risk_per_trade": params["stop_loss"],
                    "take_profit_ratio": params["take_profit"]
                }
            }
        return {}

# Global settings instance
settings = Settings()
