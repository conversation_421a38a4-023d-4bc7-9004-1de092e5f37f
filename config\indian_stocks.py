"""
Indian Stock Market Configuration
NSE and BSE stock symbols organized by sectors for adaptive trading
"""

# NIFTY 50 - Top 50 stocks by market cap
NIFTY_50 = [
    "RELIANCE.NS", "TCS.NS", "HDFCBANK.NS", "BHARTIARTL.NS", "ICICIBANK.NS",
    "INFOSYS.NS", "SBIN.NS", "LICI.NS", "ITC.NS", "HINDUNILVR.NS",
    "LT.NS", "HCLTECH.NS", "MARUTI.NS", "SUNPHARMA.NS", "TITAN.NS",
    "ONGC.NS", "TATAMOTORS.NS", "NTPC.NS", "KOTAKBANK.NS", "ASIANPAINT.NS",
    "NESTLEIND.NS", "ULTRACEMCO.NS", "DMART.NS", "BAJFINANCE.NS", "M&M.NS",
    "WIPRO.NS", "ADANIENT.NS", "HDFCLIFE.NS", "POWERGRID.NS", "LTIM.NS",
    "JSWSTEEL.NS", "TECHM.NS", "COALINDIA.NS", "TATACONSUM.NS", "INDUSINDBK.NS",
    "BAJAJFINSV.NS", "GRASIM.NS", "CIPLA.NS", "SBILIFE.NS", "BPCL.NS",
    "TATASTEEL.NS", "ADANIPORTS.NS", "APOLLOHOSP.NS", "HINDALCO.NS", "DRREDDY.NS",
    "BRITANNIA.NS", "EICHERMOT.NS", "HEROMOTOCO.NS", "DIVISLAB.NS", "TRENT.NS"
]

# Sector-wise stock classification for adaptive learning
SECTORS = {
    "IT": {
        "stocks": ["TCS.NS", "INFOSYS.NS", "HCLTECH.NS", "WIPRO.NS", "TECHM.NS", 
                  "LTIM.NS", "MINDTREE.NS", "MPHASIS.NS", "COFORGE.NS", "PERSISTENT.NS"],
        "characteristics": {
            "volatility": "medium",
            "correlation_with_global": "high",
            "news_sensitivity": "high",
            "trading_hours_impact": "global"
        }
    },
    
    "BANKING": {
        "stocks": ["HDFCBANK.NS", "ICICIBANK.NS", "SBIN.NS", "KOTAKBANK.NS", 
                  "INDUSINDBK.NS", "AXISBANK.NS", "FEDERALBNK.NS", "IDFCFIRSTB.NS"],
        "characteristics": {
            "volatility": "high",
            "correlation_with_global": "medium",
            "news_sensitivity": "very_high",
            "trading_hours_impact": "domestic"
        }
    },
    
    "PHARMA": {
        "stocks": ["SUNPHARMA.NS", "CIPLA.NS", "DRREDDY.NS", "DIVISLAB.NS", 
                  "LUPIN.NS", "BIOCON.NS", "CADILAHC.NS", "AUROPHARMA.NS"],
        "characteristics": {
            "volatility": "high",
            "correlation_with_global": "medium",
            "news_sensitivity": "very_high",
            "trading_hours_impact": "global"
        }
    },
    
    "AUTO": {
        "stocks": ["MARUTI.NS", "TATAMOTORS.NS", "M&M.NS", "EICHERMOT.NS", 
                  "HEROMOTOCO.NS", "BAJAJ-AUTO.NS", "ASHOKLEY.NS", "TVSMOTOR.NS"],
        "characteristics": {
            "volatility": "high",
            "correlation_with_global": "medium",
            "news_sensitivity": "medium",
            "trading_hours_impact": "domestic"
        }
    },
    
    "FMCG": {
        "stocks": ["HINDUNILVR.NS", "ITC.NS", "NESTLEIND.NS", "BRITANNIA.NS", 
                  "TATACONSUM.NS", "DABUR.NS", "GODREJCP.NS", "MARICO.NS"],
        "characteristics": {
            "volatility": "low",
            "correlation_with_global": "low",
            "news_sensitivity": "low",
            "trading_hours_impact": "domestic"
        }
    },
    
    "ENERGY": {
        "stocks": ["RELIANCE.NS", "ONGC.NS", "BPCL.NS", "IOCL.NS", 
                  "HINDPETRO.NS", "GAIL.NS", "COALINDIA.NS", "NTPC.NS"],
        "characteristics": {
            "volatility": "very_high",
            "correlation_with_global": "very_high",
            "news_sensitivity": "very_high",
            "trading_hours_impact": "global"
        }
    },
    
    "METALS": {
        "stocks": ["TATASTEEL.NS", "JSWSTEEL.NS", "HINDALCO.NS", "VEDL.NS", 
                  "SAIL.NS", "NMDC.NS", "JINDALSTEL.NS", "MOIL.NS"],
        "characteristics": {
            "volatility": "very_high",
            "correlation_with_global": "very_high",
            "news_sensitivity": "high",
            "trading_hours_impact": "global"
        }
    },
    
    "TELECOM": {
        "stocks": ["BHARTIARTL.NS", "IDEA.NS", "INDUS.NS"],
        "characteristics": {
            "volatility": "medium",
            "correlation_with_global": "low",
            "news_sensitivity": "high",
            "trading_hours_impact": "domestic"
        }
    }
}

# Market timings (IST)
MARKET_TIMINGS = {
    "pre_open": "09:00",
    "open": "09:15",
    "close": "15:30",
    "post_close": "16:00"
}

# Trading parameters for different sectors
SECTOR_TRADING_PARAMS = {
    "IT": {
        "max_position_size": 0.15,
        "stop_loss": 0.02,
        "take_profit": 0.04,
        "volatility_threshold": 0.025
    },
    "BANKING": {
        "max_position_size": 0.12,
        "stop_loss": 0.025,
        "take_profit": 0.05,
        "volatility_threshold": 0.03
    },
    "PHARMA": {
        "max_position_size": 0.10,
        "stop_loss": 0.03,
        "take_profit": 0.06,
        "volatility_threshold": 0.04
    },
    "AUTO": {
        "max_position_size": 0.12,
        "stop_loss": 0.025,
        "take_profit": 0.05,
        "volatility_threshold": 0.035
    },
    "FMCG": {
        "max_position_size": 0.20,
        "stop_loss": 0.015,
        "take_profit": 0.03,
        "volatility_threshold": 0.015
    },
    "ENERGY": {
        "max_position_size": 0.08,
        "stop_loss": 0.035,
        "take_profit": 0.07,
        "volatility_threshold": 0.05
    },
    "METALS": {
        "max_position_size": 0.08,
        "stop_loss": 0.04,
        "take_profit": 0.08,
        "volatility_threshold": 0.06
    },
    "TELECOM": {
        "max_position_size": 0.15,
        "stop_loss": 0.02,
        "take_profit": 0.04,
        "volatility_threshold": 0.025
    }
}

# News sources for sentiment analysis
INDIAN_NEWS_SOURCES = [
    "https://economictimes.indiatimes.com/markets/rss/markets",
    "https://www.moneycontrol.com/rss/marketstories.xml",
    "https://www.business-standard.com/rss/markets-106.rss",
    "https://www.livemint.com/rss/markets",
    "https://www.financialexpress.com/market/rss"
]

# Currency and market indices
INDICES = {
    "NIFTY50": "^NSEI",
    "SENSEX": "^BSESN",
    "BANKNIFTY": "^NSEBANK",
    "NIFTYIT": "^CNXIT",
    "NIFTYPHARMA": "^CNXPHARMA"
}

# Default trading configuration
DEFAULT_CONFIG = {
    "initial_capital": 100000,  # 1 Lakh INR
    "max_positions": 5,
    "risk_per_trade": 0.02,
    "commission": 0.0003,  # 0.03% brokerage
    "slippage": 0.0001,
    "currency": "INR"
}
