"""
Demo script to test the persistent memory system
Shows how the AI agent records and learns from experiences
"""

import asyncio
import logging
from datetime import datetime, timedelta
from colorama import init, Fore, Style
import random
import json

# Initialize colorama
init()

# Import memory components
from memory.persistent_memory import persistent_memory, LearningExperience
from memory.memory_visualizer import memory_visualizer

async def demo_memory_system():
    """Demonstrate the persistent memory system"""
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🧠 PERSISTENT MEMORY SYSTEM DEMO{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    
    # Create some sample learning experiences
    print(f"\n{Fore.GREEN}📝 Creating sample learning experiences...{Style.RESET_ALL}")
    
    sample_experiences = [
        {
            "symbol": "RELIANCE.NS",
            "sector": "ENERGY",
            "accuracy": 0.85,
            "confidence": 0.75,
            "reward": 0.12,
            "lesson": "Excellent prediction for RELIANCE.NS in energy sector during high volatility",
            "market_conditions": {
                "volatility": 0.8,
                "trend": "UPTREND",
                "overall_signal": "BUY"
            }
        },
        {
            "symbol": "TCS.NS",
            "sector": "IT",
            "accuracy": 0.65,
            "confidence": 0.80,
            "reward": -0.05,
            "lesson": "Overconfident prediction for TCS.NS - need to be more cautious in IT sector",
            "market_conditions": {
                "volatility": 0.3,
                "trend": "SIDEWAYS",
                "overall_signal": "HOLD"
            }
        },
        {
            "symbol": "HDFCBANK.NS",
            "sector": "BANKING",
            "accuracy": 0.92,
            "confidence": 0.85,
            "reward": 0.18,
            "lesson": "Outstanding prediction for HDFCBANK.NS - banking sector analysis was spot on",
            "market_conditions": {
                "volatility": 0.5,
                "trend": "STRONG_UPTREND",
                "overall_signal": "BUY"
            }
        },
        {
            "symbol": "SUNPHARMA.NS",
            "sector": "PHARMA",
            "accuracy": 0.25,
            "confidence": 0.70,
            "reward": -0.25,
            "lesson": "Poor prediction for SUNPHARMA.NS - pharma sector volatility was underestimated",
            "market_conditions": {
                "volatility": 0.9,
                "trend": "DOWNTREND",
                "overall_signal": "SELL"
            }
        },
        {
            "symbol": "MARUTI.NS",
            "sector": "AUTO",
            "accuracy": 0.78,
            "confidence": 0.65,
            "reward": 0.08,
            "lesson": "Good prediction for MARUTI.NS - auto sector showing consistent patterns",
            "market_conditions": {
                "volatility": 0.4,
                "trend": "UPTREND",
                "overall_signal": "BUY"
            }
        }
    ]
    
    # Record experiences in memory
    for i, exp_data in enumerate(sample_experiences):
        # Create learning experience
        experience = LearningExperience(
            id=f"demo_{i}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            timestamp=datetime.now() - timedelta(minutes=random.randint(1, 1440)),  # Random time in last day
            symbol=exp_data["symbol"],
            sector=exp_data["sector"],
            market_conditions=exp_data["market_conditions"],
            prediction_made={
                "action": "BUY" if exp_data["reward"] > 0 else "SELL",
                "confidence": exp_data["confidence"],
                "reasoning": f"AI prediction for {exp_data['symbol']}"
            },
            actual_outcome={
                "reward": exp_data["reward"],
                "accuracy": exp_data["accuracy"],
                "direction": "positive" if exp_data["reward"] > 0 else "negative"
            },
            accuracy_score=exp_data["accuracy"],
            reward=exp_data["reward"],
            lesson_learned=exp_data["lesson"],
            confidence_before=exp_data["confidence"],
            confidence_after=exp_data["confidence"] + (exp_data["accuracy"] - exp_data["confidence"]) * 0.1
        )
        
        # Record in persistent memory
        persistent_memory.record_experience(experience)
        print(f"✅ Recorded: {exp_data['symbol']} - Accuracy: {exp_data['accuracy']:.1%}")
    
    print(f"\n{Fore.GREEN}🔍 Analyzing memory patterns...{Style.RESET_ALL}")
    await asyncio.sleep(1)  # Give time for pattern analysis
    
    # Show memory summary
    print(f"\n{Fore.BLUE}📊 MEMORY SUMMARY{Style.RESET_ALL}")
    summary = persistent_memory.get_memory_summary()
    
    print(f"Total Experiences: {summary.get('total_experiences', 0)}")
    print(f"Total Patterns: {summary.get('total_patterns', 0)}")
    print(f"Overall Accuracy: {summary.get('overall_accuracy', 0):.1%}")
    print(f"Memory Size: {summary.get('memory_size_mb', 0):.2f} MB")
    
    # Show sector performance
    if 'sector_performance' in summary:
        print(f"\n{Fore.MAGENTA}🏭 SECTOR PERFORMANCE{Style.RESET_ALL}")
        for sector, perf in summary['sector_performance'].items():
            print(f"{sector}: {perf['accuracy']:.1%} accuracy ({perf['count']} trades)")
    
    # Show discovered patterns
    patterns = persistent_memory.patterns
    if patterns:
        print(f"\n{Fore.YELLOW}🔍 DISCOVERED PATTERNS{Style.RESET_ALL}")
        for pattern_id, pattern in list(patterns.items())[:3]:  # Show first 3
            print(f"• {pattern.description} (Success: {pattern.success_rate:.1%})")
    
    # Show NLP insights
    if hasattr(persistent_memory, 'knowledge_base') and 'nlp_insights' in persistent_memory.knowledge_base:
        nlp_data = persistent_memory.knowledge_base['nlp_insights']
        
        print(f"\n{Fore.CYAN}🤖 NLP INSIGHTS{Style.RESET_ALL}")
        
        # Common keywords
        keywords = nlp_data.get('common_keywords', {})
        if keywords:
            top_keywords = sorted(keywords.items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"Top Keywords: {', '.join([f'{k}({v})' for k, v in top_keywords])}")
        
        # Success factors
        success_factors = nlp_data.get('success_factors', [])
        if success_factors:
            print(f"Success Factors: {', '.join(success_factors[-3:])}")
        
        # Failure factors
        failure_factors = nlp_data.get('failure_factors', [])
        if failure_factors:
            print(f"Failure Factors: {', '.join(failure_factors[-3:])}")
    
    # Generate comprehensive memory report
    print(f"\n{Fore.GREEN}📋 Generating comprehensive memory report...{Style.RESET_ALL}")
    
    report = memory_visualizer.generate_memory_report()
    
    # Show key insights from report
    if 'learning_progress' in report and 'accuracy_improvement' in report['learning_progress']:
        improvement = report['learning_progress']['accuracy_improvement']
        print(f"Learning Progress: {improvement:+.1%} accuracy improvement")
    
    if 'confidence_calibration' in report:
        calibration = report['confidence_calibration']
        if 'calibration_score' in calibration:
            score = calibration['calibration_score']
            print(f"Confidence Calibration: {score:.1%} (1.0 = perfect)")
    
    if 'recommendations' in report:
        recommendations = report['recommendations']
        print(f"\n{Fore.YELLOW}💡 AI RECOMMENDATIONS{Style.RESET_ALL}")
        for i, rec in enumerate(recommendations[:3], 1):
            print(f"{i}. {rec}")
    
    # Save report to file
    report_file = memory_visualizer.save_memory_report()
    if report_file:
        print(f"\n💾 Full report saved to: {report_file}")
    
    # Test memory-based insights
    print(f"\n{Fore.CYAN}🧠 Testing Memory-Based Insights{Style.RESET_ALL}")
    
    # Test pattern matching
    test_context = {
        'sector': 'BANKING',
        'volatility': 0.5,
        'trend': 'UPTREND',
        'hour': 14
    }
    
    relevant_patterns = persistent_memory.get_relevant_patterns(test_context)
    print(f"Relevant patterns for BANKING sector: {len(relevant_patterns)}")
    
    for pattern in relevant_patterns[:2]:
        print(f"• {pattern.description} (Confidence: {pattern.confidence:.1%})")
    
    # Show memory file structure
    print(f"\n{Fore.GREEN}📁 Memory File Structure{Style.RESET_ALL}")
    memory_dir = persistent_memory.memory_dir
    
    files_created = []
    if persistent_memory.experiences_file.exists():
        files_created.append(f"experiences.jsonl ({persistent_memory.experiences_file.stat().st_size} bytes)")
    if persistent_memory.patterns_file.exists():
        files_created.append(f"patterns.json ({persistent_memory.patterns_file.stat().st_size} bytes)")
    if persistent_memory.knowledge_file.exists():
        files_created.append(f"knowledge_base.json ({persistent_memory.knowledge_file.stat().st_size} bytes)")
    if persistent_memory.db_path.exists():
        files_created.append(f"agent_memory.db ({persistent_memory.db_path.stat().st_size} bytes)")
    
    for file_info in files_created:
        print(f"✅ {file_info}")
    
    print(f"\n{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🎉 MEMORY SYSTEM DEMO COMPLETED!{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}Key Features Demonstrated:{Style.RESET_ALL}")
    print("✅ Persistent experience recording with NLP enhancement")
    print("✅ Automatic pattern discovery and analysis")
    print("✅ Sector-specific performance tracking")
    print("✅ NLP-powered insight extraction")
    print("✅ Confidence calibration analysis")
    print("✅ Memory-based recommendations")
    print("✅ Comprehensive reporting and visualization")
    print("✅ Multiple storage formats (JSON, SQLite)")
    
    print(f"\n{Fore.YELLOW}The AI agent now has persistent memory that:")
    print("• Records every trading experience with detailed context")
    print("• Discovers patterns automatically using NLP")
    print("• Provides memory-enhanced decision making")
    print("• Generates actionable insights and recommendations")
    print("• Continuously improves through accumulated knowledge")
    
    print(f"\n{Fore.GREEN}Memory files are saved in the 'memory/' directory")
    print(f"Reports are saved in the 'reports/' directory{Style.RESET_ALL}")

if __name__ == "__main__":
    # Setup basic logging
    logging.basicConfig(level=logging.INFO)
    
    # Run the demo
    asyncio.run(demo_memory_system())
