"""
Revolutionary Intraday Trading AI Agent
Zero-shot learning with real-time market predictions and continuous improvement

This is a cutting-edge AI agent that:
1. Makes real-time predictions on live Indian stock market data
2. Learns from its mistakes using reinforcement learning
3. Adapts to different market sectors and conditions
4. Uses advanced tool calling for market analysis
5. Optimized for 4GB RAM and older PCs
"""

import asyncio
import logging
import sys
import signal
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any
import argparse
import json
import os
from colorama import init, Fore, Style
import schedule

# Initialize colorama for colored output
init()

# Import our modules
from environment.live_prediction_env import live_env, PredictionAction
from agents.meta_rl_agent import meta_agent
from data.market_data_fetcher import get_market_data_fetcher
from tools.market_analyzer import MarketAnalyzer
from config.indian_stocks import NIFTY_50, SECTORS
from config.settings import settings

class TradingAIOrchestrator:
    """
    Main orchestrator for the revolutionary trading AI agent
    """
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        self.is_running = False
        self.market_analyzer = MarketAnalyzer()
        self.prediction_interval = 60  # seconds between predictions
        self.learning_interval = 300  # seconds between learning updates
        
        # Performance tracking
        self.session_start_time = datetime.now()
        self.predictions_made = 0
        self.learning_updates = 0
        
        # Graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
    def setup_logging(self):
        """Setup comprehensive logging"""
        log_format = '%(asctime)s | %(levelname)s | %(name)s | %(message)s'
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(
            f'logs/trading_ai_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'
        )
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter(log_format))
        
        # Console handler with colors
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter(log_format))
        
        # Root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        root_logger.addHandler(file_handler)
        root_logger.addHandler(console_handler)
    
    def signal_handler(self, signum, frame):
        """Handle graceful shutdown"""
        self.logger.info(f"🛑 Received signal {signum}, shutting down gracefully...")
        self.is_running = False
    
    async def start_agent(self, symbols: List[str] = None, mode: str = "live"):
        """
        Start the revolutionary trading AI agent
        
        Args:
            symbols: List of symbols to trade (default: top NIFTY 50)
            mode: "live", "demo", or "backtest"
        """
        try:
            self.logger.info("🚀 STARTING REVOLUTIONARY TRADING AI AGENT")
            self.logger.info("=" * 60)
            self.logger.info(f"Mode: {mode.upper()}")
            self.logger.info(f"Session: {self.session_start_time}")
            self.logger.info("=" * 60)
            
            # Default symbols
            if symbols is None:
                symbols = NIFTY_50[:10]  # Start with top 10 for efficiency
            
            self.logger.info(f"📊 Tracking symbols: {', '.join(symbols)}")
            
            # Initialize components
            await self._initialize_components()
            
            # Start live prediction environment
            await live_env.start_live_environment()
            
            # Main trading loop
            self.is_running = True
            await self._run_trading_loop(symbols, mode)
            
        except Exception as e:
            self.logger.error(f"❌ Error starting agent: {e}")
            raise
        finally:
            await self._cleanup()
    
    async def _initialize_components(self):
        """Initialize all AI components"""
        self.logger.info("🔧 Initializing AI components...")
        
        # Check system resources
        self._check_system_resources()
        
        # Initialize market data fetcher
        self.logger.info("📡 Initializing market data fetcher...")
        
        # Load any existing model
        model_path = "models/meta_agent_latest.pth"
        if os.path.exists(model_path):
            self.logger.info("📂 Loading existing model...")
            meta_agent.load_model(model_path)
        else:
            self.logger.info("🆕 Starting with fresh model (zero-shot learning)")
        
        self.logger.info("✅ All components initialized successfully")
    
    def _check_system_resources(self):
        """Check if system meets minimum requirements"""
        try:
            import psutil
            
            # Memory check
            memory = psutil.virtual_memory()
            available_gb = memory.available / (1024**3)
            
            if available_gb < 2.0:
                self.logger.warning(f"⚠️  Low memory: {available_gb:.1f}GB available")
                self.logger.info("🔧 Applying memory optimizations...")
                
                # Apply memory optimizations
                settings.model.batch_size = 16
                settings.rl.replay_buffer_size = 5000
                settings.optimization.memory_limit_mb = 2000
            
            # CPU check
            cpu_count = psutil.cpu_count()
            self.logger.info(f"💻 System: {cpu_count} CPU cores, {memory.total/(1024**3):.1f}GB RAM")
            
        except ImportError:
            self.logger.warning("⚠️  psutil not available, skipping resource check")
    
    async def _run_trading_loop(self, symbols: List[str], mode: str):
        """Main trading loop with real-time predictions"""
        self.logger.info("🎯 Starting real-time prediction loop...")
        
        last_prediction_time = 0
        last_learning_time = 0
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # Make predictions at regular intervals
                if current_time - last_prediction_time >= self.prediction_interval:
                    await self._make_predictions_round(symbols)
                    last_prediction_time = current_time
                    self.predictions_made += 1
                
                # Learning updates at regular intervals
                if current_time - last_learning_time >= self.learning_interval:
                    await self._learning_update()
                    last_learning_time = current_time
                    self.learning_updates += 1
                
                # Status update every 5 minutes
                if self.predictions_made % 5 == 0 and self.predictions_made > 0:
                    await self._print_status_update()
                
                # Short sleep to prevent CPU overload
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"❌ Error in trading loop: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _make_predictions_round(self, symbols: List[str]):
        """Make predictions for all symbols"""
        try:
            self.logger.info(f"🎯 Making prediction round {self.predictions_made + 1}")
            
            for symbol in symbols:
                try:
                    # Get current market data
                    fetcher = get_market_data_fetcher()
                    market_data = await fetcher.fetch_realtime_data(symbol)
                    
                    if not market_data:
                        self.logger.warning(f"⚠️  No market data for {symbol}")
                        continue
                    
                    # Prepare market data for analysis
                    market_data_dict = {
                        'current_price': {
                            'close': market_data.close,
                            'high': market_data.high,
                            'low': market_data.low,
                            'volume': market_data.volume
                        },
                        'timestamp': market_data.timestamp.isoformat()
                    }
                    
                    # Get technical analysis
                    technical_analysis = self.market_analyzer.analyze_symbol(symbol)
                    
                    # Add technical analysis to market data
                    market_data_dict['technical_analysis'] = {
                        'overall_signal': technical_analysis.overall_signal,
                        'confidence': technical_analysis.confidence,
                        'trend_direction': technical_analysis.trend_direction,
                        'volatility_score': technical_analysis.volatility_score
                    }
                    
                    # Make AI prediction
                    action, confidence, reasoning = await meta_agent.make_prediction(
                        symbol, market_data_dict
                    )
                    
                    # Calculate expected price change based on action
                    expected_change = self._calculate_expected_change(action, confidence)
                    
                    # Submit prediction to live environment
                    prediction_id = live_env.make_prediction(
                        symbol=symbol,
                        action=action,
                        confidence=confidence,
                        predicted_change=expected_change,
                        timeframe=5,  # 5-minute prediction
                        reasoning=reasoning
                    )
                    
                    if prediction_id:
                        self.logger.info(f"✅ Prediction made for {symbol}: {action.name} "
                                       f"(confidence: {confidence:.2f})")
                    
                    # Small delay between symbols to avoid rate limiting
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    self.logger.error(f"❌ Error making prediction for {symbol}: {e}")
            
        except Exception as e:
            self.logger.error(f"❌ Error in prediction round: {e}")
    
    def _calculate_expected_change(self, action: PredictionAction, confidence: float) -> float:
        """Calculate expected price change based on action and confidence"""
        base_changes = {
            PredictionAction.STRONG_BUY: 2.0,
            PredictionAction.BUY: 1.0,
            PredictionAction.HOLD: 0.0,
            PredictionAction.SELL: -1.0,
            PredictionAction.STRONG_SELL: -2.0
        }
        
        base_change = base_changes.get(action, 0.0)
        return base_change * confidence

    async def _learning_update(self):
        """Perform learning update from recent experiences"""
        try:
            self.logger.info("🎓 Performing learning update...")

            # Get learning experiences from live environment
            experiences = live_env.get_learning_experience()

            if experiences:
                # Train the meta-learning agent
                meta_agent.learn_from_experience(experiences)

                self.logger.info(f"📚 Learned from {len(experiences)} experiences")

                # Save model periodically
                if self.learning_updates % 5 == 0:
                    model_path = f"models/meta_agent_step_{self.learning_updates}.pth"
                    os.makedirs("models", exist_ok=True)
                    meta_agent.save_model(model_path)

                    # Also save as latest
                    meta_agent.save_model("models/meta_agent_latest.pth")
            else:
                self.logger.info("📚 No new experiences to learn from")

        except Exception as e:
            self.logger.error(f"❌ Error in learning update: {e}")

    async def _print_status_update(self):
        """Print comprehensive status update"""
        try:
            # Get performance summary
            performance = live_env.get_performance_summary()

            # Calculate session duration
            session_duration = datetime.now() - self.session_start_time

            print(f"\n{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}🤖 TRADING AI STATUS UPDATE{Style.RESET_ALL}")
            print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")

            # Session info
            print(f"⏱️  Session Duration: {session_duration}")
            print(f"🎯 Predictions Made: {self.predictions_made}")
            print(f"🎓 Learning Updates: {self.learning_updates}")

            # Performance metrics
            metrics = performance['metrics']
            print(f"\n{Fore.GREEN}📊 PERFORMANCE METRICS{Style.RESET_ALL}")
            print(f"Total Predictions: {metrics['total_predictions']}")
            print(f"Accuracy Rate: {metrics['accuracy_rate']:.1%}")
            print(f"Average Confidence: {metrics['average_confidence']:.2f}")
            print(f"Average Reward: {metrics['average_reward']:+.3f}")
            print(f"Learning Velocity: {metrics['learning_velocity']:+.3f}")

            # Active predictions
            print(f"\n{Fore.BLUE}🎯 ACTIVE PREDICTIONS{Style.RESET_ALL}")
            print(f"Currently Tracking: {performance['active_predictions']} predictions")

            # Top performing symbols
            top_symbols = performance.get('top_performing_symbols', [])
            if top_symbols:
                print(f"\n{Fore.MAGENTA}🏆 TOP PERFORMING SYMBOLS{Style.RESET_ALL}")
                for symbol, perf in top_symbols[:3]:
                    print(f"{symbol}: {perf['accuracy']:.1%} accuracy, "
                          f"{perf['avg_reward']:+.3f} avg reward")

            # Learning insights
            insights = performance.get('learning_insights', {})
            if insights:
                print(f"\n{Fore.YELLOW}🧠 LEARNING INSIGHTS{Style.RESET_ALL}")
                for key, value in insights.items():
                    print(f"{key}: {value}")

            print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}\n")

        except Exception as e:
            self.logger.error(f"❌ Error printing status update: {e}")

    async def _cleanup(self):
        """Cleanup resources"""
        try:
            self.logger.info("🧹 Cleaning up resources...")

            # Stop live environment
            live_env.stop()

            # Close market data fetcher
            fetcher = get_market_data_fetcher()
            if fetcher:
                await fetcher.close()

            # Save final model
            model_path = f"models/meta_agent_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pth"
            os.makedirs("models", exist_ok=True)
            meta_agent.save_model(model_path)

            # Print final summary
            await self._print_final_summary()

            self.logger.info("✅ Cleanup completed successfully")

        except Exception as e:
            self.logger.error(f"❌ Error during cleanup: {e}")

    async def _print_final_summary(self):
        """Print final session summary"""
        try:
            performance = live_env.get_performance_summary()
            session_duration = datetime.now() - self.session_start_time

            print(f"\n{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}🎉 FINAL SESSION SUMMARY{Style.RESET_ALL}")
            print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")

            print(f"⏱️  Total Session Time: {session_duration}")
            print(f"🎯 Total Predictions: {self.predictions_made}")
            print(f"🎓 Learning Updates: {self.learning_updates}")

            metrics = performance['metrics']
            print(f"\n📊 Final Accuracy: {metrics['accuracy_rate']:.1%}")
            print(f"🎯 Final Confidence: {metrics['average_confidence']:.2f}")
            print(f"💰 Final Avg Reward: {metrics['average_reward']:+.3f}")
            print(f"📈 Learning Velocity: {metrics['learning_velocity']:+.3f}")

            print(f"\n💾 Model saved to: models/meta_agent_final_*.pth")
            print(f"📊 Logs saved to: logs/trading_ai_*.log")

            print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Thank you for using the Revolutionary Trading AI!{Style.RESET_ALL}")
            print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}\n")

        except Exception as e:
            self.logger.error(f"❌ Error printing final summary: {e}")

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Revolutionary Intraday Trading AI Agent")
    parser.add_argument("--symbols", nargs="+", help="Stock symbols to trade")
    parser.add_argument("--mode", choices=["live", "demo", "backtest"],
                       default="live", help="Trading mode")
    parser.add_argument("--sector", choices=list(SECTORS.keys()),
                       help="Focus on specific sector")

    args = parser.parse_args()

    # Determine symbols to trade
    symbols = args.symbols
    if args.sector:
        symbols = SECTORS[args.sector]["stocks"][:5]  # Top 5 in sector
    elif not symbols:
        symbols = NIFTY_50[:10]  # Default to top 10

    # Create and start the orchestrator
    orchestrator = TradingAIOrchestrator()

    try:
        await orchestrator.start_agent(symbols, args.mode)
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}👋 Goodbye! Agent stopped by user.{Style.RESET_ALL}")
    except Exception as e:
        print(f"\n{Fore.RED}❌ Fatal error: {e}{Style.RESET_ALL}")
        sys.exit(1)

if __name__ == "__main__":
    # Print welcome message
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🚀 REVOLUTIONARY INTRADAY TRADING AI AGENT{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Zero-Shot Learning | Real-Time Predictions | Continuous Improvement{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}\n")

    # Run the main function
    asyncio.run(main())
