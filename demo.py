"""
Demo <PERSON>ript for Revolutionary Trading AI Agent
Shows the agent making real-time predictions and learning from market data
"""

import asyncio
import logging
import pandas as pd
from datetime import datetime
from colorama import init, Fore, Style

# Initialize colorama
init()

# Import our modules
from data.market_data_fetcher import get_market_data_fetcher
from agents.meta_rl_agent import meta_agent
from environment.live_prediction_env import live_env, PredictionAction
from tools.market_analyzer import MarketAnalyzer
from config.indian_stocks import NIFTY_50

async def demo_data_fetching():
    """Demo: Fetch real-time market data"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📡 DEMO: Real-Time Market Data Fetching{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    # Test symbols
    test_symbols = ["RELIANCE.NS", "TCS.NS", "HDFCBANK.NS"]
    
    for symbol in test_symbols:
        print(f"\n🔍 Fetching data for {symbol}...")
        
        try:
            # Fetch real-time data
            fetcher = get_market_data_fetcher()
            market_data = await fetcher.fetch_realtime_data(symbol)
            
            if market_data:
                print(f"✅ {symbol}:")
                print(f"   Price: ₹{market_data.close:.2f}")
                print(f"   Volume: {market_data.volume:,}")
                print(f"   Source: {market_data.source}")
                print(f"   Time: {market_data.timestamp}")
            else:
                print(f"❌ No data available for {symbol}")
                
        except Exception as e:
            print(f"❌ Error fetching {symbol}: {e}")
    
    print(f"\n{Fore.GREEN}✅ Data fetching demo completed{Style.RESET_ALL}")

async def demo_technical_analysis():
    """Demo: Technical analysis capabilities"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📊 DEMO: Technical Analysis{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    analyzer = MarketAnalyzer()
    test_symbol = "RELIANCE.NS"
    
    print(f"\n🔍 Analyzing {test_symbol}...")
    
    try:
        # Perform technical analysis
        analysis = analyzer.analyze_symbol(test_symbol, timeframe="1h", lookback_days=30)
        
        print(f"✅ Technical Analysis for {test_symbol}:")
        print(f"   Overall Signal: {analysis.overall_signal}")
        print(f"   Confidence: {analysis.confidence:.2f}")
        print(f"   Trend: {analysis.trend_direction}")
        print(f"   Volatility: {analysis.volatility_score:.2f}")
        
        print(f"\n📈 Technical Signals:")
        for signal in analysis.technical_signals[:5]:  # Show top 5
            print(f"   {signal.indicator}: {signal.signal} "
                  f"(strength: {signal.strength:.2f}) - {signal.description}")
        
        if analysis.support_levels:
            print(f"\n🛡️  Support Levels: {[f'₹{level:.2f}' for level in analysis.support_levels]}")
        
        if analysis.resistance_levels:
            print(f"🚧 Resistance Levels: {[f'₹{level:.2f}' for level in analysis.resistance_levels]}")
        
    except Exception as e:
        print(f"❌ Error in technical analysis: {e}")
    
    print(f"\n{Fore.GREEN}✅ Technical analysis demo completed{Style.RESET_ALL}")

async def demo_ai_predictions():
    """Demo: AI agent making predictions"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🤖 DEMO: AI Prediction Engine{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    test_symbols = ["RELIANCE.NS", "TCS.NS"]
    
    for symbol in test_symbols:
        print(f"\n🎯 Making AI prediction for {symbol}...")
        
        try:
            # Get market data
            fetcher = get_market_data_fetcher()
            market_data = await fetcher.fetch_realtime_data(symbol)
            
            if not market_data:
                print(f"❌ No market data for {symbol}")
                continue
            
            # Prepare data for AI
            market_data_dict = {
                'current_price': {
                    'close': market_data.close,
                    'high': market_data.high,
                    'low': market_data.low,
                    'volume': market_data.volume
                },
                'timestamp': market_data.timestamp.isoformat()
            }
            
            # Make AI prediction
            action, confidence, reasoning = await meta_agent.make_prediction(
                symbol, market_data_dict
            )
            
            print(f"✅ AI Prediction for {symbol}:")
            print(f"   Action: {action.name}")
            print(f"   Confidence: {confidence:.2f}")
            print(f"   Reasoning: {reasoning}")
            
        except Exception as e:
            print(f"❌ Error making prediction for {symbol}: {e}")
    
    print(f"\n{Fore.GREEN}✅ AI prediction demo completed{Style.RESET_ALL}")

async def demo_live_environment():
    """Demo: Live prediction environment"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🎯 DEMO: Live Prediction Environment{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    try:
        # Start live environment
        print("🚀 Starting live prediction environment...")
        await live_env.start_live_environment()
        
        # Make a few test predictions
        test_symbol = "RELIANCE.NS"
        
        print(f"\n📊 Making test predictions for {test_symbol}...")
        
        # Prediction 1: Buy signal
        pred_id_1 = live_env.make_prediction(
            symbol=test_symbol,
            action=PredictionAction.BUY,
            confidence=0.75,
            predicted_change=1.5,
            timeframe=5,
            reasoning="Demo prediction: Bullish technical signals"
        )
        
        # Prediction 2: Sell signal
        pred_id_2 = live_env.make_prediction(
            symbol=test_symbol,
            action=PredictionAction.SELL,
            confidence=0.60,
            predicted_change=-1.0,
            timeframe=3,
            reasoning="Demo prediction: Bearish momentum"
        )
        
        print(f"✅ Made predictions: {pred_id_1}, {pred_id_2}")
        
        # Wait a bit and show status
        await asyncio.sleep(2)
        
        # Get performance summary
        performance = live_env.get_performance_summary()
        
        print(f"\n📈 Environment Status:")
        print(f"   Active Predictions: {performance['active_predictions']}")
        print(f"   Total Predictions: {performance['metrics']['total_predictions']}")
        print(f"   Accuracy Rate: {performance['metrics']['accuracy_rate']:.1%}")
        
        # Stop environment
        live_env.stop()
        
    except Exception as e:
        print(f"❌ Error in live environment demo: {e}")
    
    print(f"\n{Fore.GREEN}✅ Live environment demo completed{Style.RESET_ALL}")

async def demo_sector_analysis():
    """Demo: Sector-wise analysis"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🏭 DEMO: Sector Analysis{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    analyzer = MarketAnalyzer()
    test_sector = "IT"
    
    print(f"\n🔍 Analyzing {test_sector} sector...")
    
    try:
        # Analyze IT sector (first 3 stocks for demo)
        from config.indian_stocks import SECTORS
        it_stocks = SECTORS["IT"]["stocks"][:3]
        
        sector_analyses = {}
        for stock in it_stocks:
            try:
                analysis = analyzer.analyze_symbol(stock)
                sector_analyses[stock] = analysis
                print(f"   {stock}: {analysis.overall_signal} "
                      f"(confidence: {analysis.confidence:.2f})")
            except Exception as e:
                print(f"   {stock}: Error - {e}")
        
        # Get overall sector sentiment
        if sector_analyses:
            sentiment = analyzer.get_market_sentiment(sector_analyses)
            
            print(f"\n📊 {test_sector} Sector Sentiment:")
            print(f"   Overall: {sentiment['sentiment']}")
            print(f"   Confidence: {sentiment['confidence']:.2f}")
            print(f"   Bullish %: {sentiment['bullish_percentage']:.1f}%")
            print(f"   Buy Signals: {sentiment['buy_signals']}")
            print(f"   Sell Signals: {sentiment['sell_signals']}")
        
    except Exception as e:
        print(f"❌ Error in sector analysis: {e}")
    
    print(f"\n{Fore.GREEN}✅ Sector analysis demo completed{Style.RESET_ALL}")

async def demo_training_data():
    """Demo: Get today's training data"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📚 DEMO: Training Data Collection{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    print("📊 Collecting today's market data for training...")
    
    try:
        # Get training data for top 3 stocks
        training_symbols = NIFTY_50[:3]
        fetcher = get_market_data_fetcher()
        training_data = fetcher.get_today_data_for_training(training_symbols)
        
        print(f"\n✅ Training Data Summary:")
        for symbol, data in training_data.items():
            if not data.empty:
                print(f"   {symbol}: {len(data)} data points")
                print(f"      Latest price: ₹{data['Close'].iloc[-1]:.2f}")
                print(f"      Price range: ₹{data['Low'].min():.2f} - ₹{data['High'].max():.2f}")
                
                # Show some technical indicators if available
                if 'RSI' in data.columns:
                    latest_rsi = data['RSI'].iloc[-1]
                    if not pd.isna(latest_rsi):
                        print(f"      Latest RSI: {latest_rsi:.1f}")
            else:
                print(f"   {symbol}: No data available")
        
    except Exception as e:
        print(f"❌ Error collecting training data: {e}")
    
    print(f"\n{Fore.GREEN}✅ Training data demo completed{Style.RESET_ALL}")

async def run_full_demo():
    """Run the complete demo"""
    print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🚀 REVOLUTIONARY TRADING AI - COMPLETE DEMO{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}This demo showcases the cutting-edge capabilities of our AI agent{Style.RESET_ALL}")
    print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
    
    try:
        # Run all demo components
        await demo_data_fetching()
        await demo_technical_analysis()
        await demo_ai_predictions()
        await demo_live_environment()
        await demo_sector_analysis()
        await demo_training_data()
        
        # Final summary
        print(f"\n{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🎉 DEMO COMPLETED SUCCESSFULLY!{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Key Features Demonstrated:{Style.RESET_ALL}")
        print(f"✅ Real-time market data fetching from multiple sources")
        print(f"✅ Advanced technical analysis with multiple indicators")
        print(f"✅ AI-powered prediction engine with reasoning")
        print(f"✅ Live prediction environment with learning capabilities")
        print(f"✅ Sector-wise analysis and sentiment calculation")
        print(f"✅ Training data collection for continuous learning")
        print(f"\n{Fore.YELLOW}Ready to start live trading with: python main.py{Style.RESET_ALL}")
        print(f"{Fore.GREEN}{'='*80}{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"\n{Fore.RED}❌ Demo error: {e}{Style.RESET_ALL}")
    finally:
        # Cleanup
        fetcher = get_market_data_fetcher()
        if fetcher:
            await fetcher.close()

if __name__ == "__main__":
    # Setup basic logging for demo
    logging.basicConfig(level=logging.WARNING)  # Reduce log noise for demo
    
    # Run the demo
    asyncio.run(run_full_demo())
