#!/usr/bin/env python3
"""
Quick Training Status Checker
See your AI agent's current learning progress
"""

from colorama import init, Fore, Style
from datetime import datetime
import json

# Initialize colorama
init()

def print_status_banner():
    """Print status banner"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📊 AI AGENT TRAINING STATUS{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

def check_portfolio_status():
    """Check portfolio status"""
    try:
        from portfolio.portfolio_manager import portfolio_manager
        
        summary = portfolio_manager.get_portfolio_summary()
        
        print(f"\n{Fore.GREEN}💰 PORTFOLIO STATUS{Style.RESET_ALL}")
        print(f"💵 Current Value: ₹{summary['total_value']:,.2f}")
        print(f"💸 Cash Balance: ₹{summary['cash_balance']:,.2f}")
        print(f"📈 Positions Value: ₹{summary['positions_value']:,.2f}")
        print(f"💹 Total P&L: ₹{summary['total_pnl']:+,.2f}")
        print(f"📊 Return: {summary['total_return_pct']:+.2f}%")
        print(f"🎯 Total Trades: {summary['total_trades']}")
        print(f"🏆 Win Rate: {summary['win_rate']:.1f}%")
        
        # Performance assessment
        if summary['total_return_pct'] > 10:
            print(f"{Fore.GREEN}🎉 Excellent performance! Agent is learning well!{Style.RESET_ALL}")
        elif summary['total_return_pct'] > 5:
            print(f"{Fore.YELLOW}📈 Good performance! Keep training!{Style.RESET_ALL}")
        elif summary['total_return_pct'] > 0:
            print(f"{Fore.BLUE}📊 Positive returns. Agent is improving!{Style.RESET_ALL}")
        elif summary['total_trades'] == 0:
            print(f"{Fore.CYAN}🚀 Ready to start training!{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}📉 Learning phase. More training needed!{Style.RESET_ALL}")
        
        return summary
        
    except Exception as e:
        print(f"{Fore.RED}❌ Portfolio status error: {e}{Style.RESET_ALL}")
        return None

def check_memory_status():
    """Check memory and learning status"""
    try:
        from memory.persistent_memory import persistent_memory
        
        experiences = persistent_memory.experiences
        patterns = persistent_memory.patterns
        
        print(f"\n{Fore.GREEN}🧠 MEMORY & LEARNING STATUS{Style.RESET_ALL}")
        print(f"📚 Total Experiences: {len(experiences)}")
        print(f"🔍 Patterns Discovered: {len(patterns)}")
        
        if experiences:
            # Calculate accuracy metrics
            total_accuracy = sum(exp.accuracy_score for exp in experiences) / len(experiences)
            print(f"🎯 Overall Accuracy: {total_accuracy:.1%}")
            
            # Recent performance
            if len(experiences) >= 10:
                recent_accuracy = sum(exp.accuracy_score for exp in experiences[-10:]) / 10
                early_accuracy = sum(exp.accuracy_score for exp in experiences[:10]) / 10
                improvement = recent_accuracy - early_accuracy
                
                print(f"📈 Recent Accuracy: {recent_accuracy:.1%}")
                print(f"📊 Improvement: {improvement:+.1%}")
                
                if improvement > 0.1:
                    print(f"{Fore.GREEN}🚀 Agent is learning rapidly!{Style.RESET_ALL}")
                elif improvement > 0:
                    print(f"{Fore.YELLOW}📈 Agent is improving steadily{Style.RESET_ALL}")
                else:
                    print(f"{Fore.BLUE}📊 Agent performance is stable{Style.RESET_ALL}")
            
            # Sector analysis
            from collections import defaultdict
            sector_performance = defaultdict(list)
            
            for exp in experiences:
                sector_performance[exp.sector].append(exp.accuracy_score)
            
            if sector_performance:
                print(f"\n{Fore.BLUE}🏭 SECTOR PERFORMANCE{Style.RESET_ALL}")
                sorted_sectors = sorted(
                    [(sector, sum(scores)/len(scores), len(scores)) 
                     for sector, scores in sector_performance.items()],
                    key=lambda x: x[1], reverse=True
                )
                
                for sector, avg_accuracy, count in sorted_sectors[:5]:
                    print(f"{sector}: {avg_accuracy:.1%} accuracy ({count} trades)")
        
        else:
            print(f"{Fore.YELLOW}📝 No experiences yet. Start training to build memory!{Style.RESET_ALL}")
        
        return len(experiences), len(patterns)
        
    except Exception as e:
        print(f"{Fore.RED}❌ Memory status error: {e}{Style.RESET_ALL}")
        return 0, 0

def check_visualization_files():
    """Check available visualization files"""
    try:
        from pathlib import Path
        
        chart_dir = Path("visualization/charts")
        
        if not chart_dir.exists():
            print(f"\n{Fore.YELLOW}📊 No visualization files yet. Run training to generate charts!{Style.RESET_ALL}")
            return []
        
        files = list(chart_dir.glob("*"))
        
        if not files:
            print(f"\n{Fore.YELLOW}📊 No visualization files yet. Run training to generate charts!{Style.RESET_ALL}")
            return []
        
        print(f"\n{Fore.GREEN}📊 VISUALIZATION FILES{Style.RESET_ALL}")
        
        # Group by type
        html_files = [f for f in files if f.suffix == '.html']
        png_files = [f for f in files if f.suffix == '.png']
        
        print(f"📈 Interactive Charts: {len(html_files)}")
        print(f"🖼️  Summary Images: {len(png_files)}")
        
        # Show recent files
        recent_files = sorted(files, key=lambda x: x.stat().st_mtime, reverse=True)[:3]
        print(f"\n📋 Recent Files:")
        for file in recent_files:
            file_age = datetime.now() - datetime.fromtimestamp(file.stat().st_mtime)
            print(f"  📊 {file.name} ({file_age.total_seconds()/3600:.1f}h ago)")
        
        return files
        
    except Exception as e:
        print(f"{Fore.RED}❌ Visualization check error: {e}{Style.RESET_ALL}")
        return []

def get_training_recommendations():
    """Get training recommendations based on current status"""
    print(f"\n{Fore.YELLOW}💡 TRAINING RECOMMENDATIONS{Style.RESET_ALL}")
    
    try:
        # Get current status
        from portfolio.portfolio_manager import portfolio_manager
        from memory.persistent_memory import persistent_memory
        
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        experiences = persistent_memory.experiences
        
        recommendations = []
        
        # Based on experience count
        if len(experiences) == 0:
            recommendations.append("🚀 Start with Quick Training (10 predictions) to get familiar")
            recommendations.append("📊 Run demo first to see the system in action")
        elif len(experiences) < 20:
            recommendations.append("📈 Continue with Standard Training (50 predictions)")
            recommendations.append("🎯 Focus on building consistent prediction patterns")
        elif len(experiences) < 50:
            recommendations.append("🏆 Try Intensive Training (100 predictions) for deep learning")
            recommendations.append("🧠 Check memory insights for discovered patterns")
        else:
            recommendations.append("🔄 Use Continuous Training for ongoing improvement")
            recommendations.append("📊 Analyze performance and adjust strategy")
        
        # Based on portfolio performance
        if portfolio_summary['total_trades'] == 0:
            recommendations.append("💰 No trades yet - agent needs more confident predictions")
        elif portfolio_summary['total_return_pct'] < 0:
            recommendations.append("📉 Negative returns - consider adjusting prediction thresholds")
        elif portfolio_summary['win_rate'] < 50:
            recommendations.append("🎯 Low win rate - focus on improving prediction accuracy")
        
        # Based on accuracy
        if experiences:
            recent_accuracy = sum(exp.accuracy_score for exp in experiences[-10:]) / min(10, len(experiences))
            if recent_accuracy < 0.6:
                recommendations.append("🎲 Low accuracy - agent needs more training data")
            elif recent_accuracy > 0.8:
                recommendations.append("🎉 High accuracy - ready for live trading!")
        
        # Show recommendations
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"{i}. {rec}")
        
        if not recommendations:
            print("🎯 Agent is performing well! Continue current training approach.")
        
    except Exception as e:
        print(f"⚠️  Recommendation error: {e}")

def show_quick_actions():
    """Show quick action options"""
    print(f"\n{Fore.CYAN}⚡ QUICK ACTIONS{Style.RESET_ALL}")
    print("1. 🚀 Start Quick Training")
    print("2. 📊 Open Latest Visualization")
    print("3. 🧠 View Memory Report")
    print("4. 💰 Detailed Portfolio Analysis")
    print("5. 🔄 Refresh Status")

def main():
    """Main status check function"""
    print_status_banner()
    
    # Check all systems
    portfolio_summary = check_portfolio_status()
    experience_count, pattern_count = check_memory_status()
    viz_files = check_visualization_files()
    
    # Overall assessment
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📋 OVERALL ASSESSMENT{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    if portfolio_summary:
        total_trades = portfolio_summary['total_trades']
        total_return = portfolio_summary['total_return_pct']
        
        if total_trades == 0 and experience_count == 0:
            print(f"🚀 {Fore.GREEN}Ready to start! Run training to begin learning.{Style.RESET_ALL}")
        elif experience_count < 10:
            print(f"🌱 {Fore.YELLOW}Early learning phase. Keep training!{Style.RESET_ALL}")
        elif experience_count < 50:
            print(f"📈 {Fore.BLUE}Good progress! Agent is building knowledge.{Style.RESET_ALL}")
        else:
            print(f"🧠 {Fore.GREEN}Experienced agent! Ready for advanced training.{Style.RESET_ALL}")
    
    # Get recommendations
    get_training_recommendations()
    
    # Show quick actions
    show_quick_actions()
    
    # Interactive options
    while True:
        try:
            choice = input(f"\n{Fore.CYAN}Choose action (1-5, or 'q' to quit): {Style.RESET_ALL}").strip().lower()
            
            if choice == 'q' or choice == 'quit':
                break
            elif choice == '1':
                print(f"\n{Fore.GREEN}Starting Quick Training...{Style.RESET_ALL}")
                import subprocess
                subprocess.run(["python", "train_agent.py"])
                break
            elif choice == '2':
                try:
                    import webbrowser
                    from pathlib import Path
                    chart_dir = Path("visualization/charts")
                    html_files = list(chart_dir.glob("*.html"))
                    if html_files:
                        latest = max(html_files, key=lambda x: x.stat().st_mtime)
                        webbrowser.open(f"file://{latest.absolute()}")
                        print(f"🌐 Opened: {latest.name}")
                    else:
                        print("No visualization files found!")
                except Exception as e:
                    print(f"Error opening visualization: {e}")
            elif choice == '3':
                try:
                    from memory.memory_visualizer import memory_visualizer
                    report = memory_visualizer.generate_memory_report()
                    print(f"📊 Memory report generated!")
                except Exception as e:
                    print(f"Error generating memory report: {e}")
            elif choice == '4':
                check_portfolio_status()
            elif choice == '5':
                print(f"\n{Fore.YELLOW}🔄 Refreshing status...{Style.RESET_ALL}")
                main()
                break
            else:
                print(f"{Fore.RED}Invalid choice. Enter 1-5 or 'q' to quit.{Style.RESET_ALL}")
        
        except KeyboardInterrupt:
            break
    
    print(f"\n{Fore.GREEN}📊 Status check completed!{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
