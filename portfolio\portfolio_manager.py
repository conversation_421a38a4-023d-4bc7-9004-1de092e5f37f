"""
Portfolio Management System for Trading AI Agent
Tracks real money simulation with ₹1000 budget
"""

import json
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging
from pathlib import Path

@dataclass
class Trade:
    """Individual trade record"""
    trade_id: str
    timestamp: datetime
    symbol: str
    action: str  # "BUY" or "SELL"
    quantity: int
    price: float
    amount: float
    commission: float
    prediction_confidence: float
    reasoning: str

@dataclass
class Position:
    """Current position in a stock"""
    symbol: str
    quantity: int
    avg_buy_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float

@dataclass
class PortfolioSnapshot:
    """Portfolio state at a point in time"""
    timestamp: datetime
    cash_balance: float
    total_value: float
    positions: Dict[str, Position]
    total_pnl: float
    day_pnl: float
    trades_count: int

class PortfolioManager:
    """
    Manages virtual portfolio with ₹1000 budget
    Tracks all trades, P&L, and portfolio performance
    """
    
    def __init__(self, initial_capital: float = 1000.0):
        self.logger = logging.getLogger(__name__)
        
        # Portfolio state
        self.initial_capital = initial_capital
        self.cash_balance = initial_capital
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        
        # Performance tracking
        self.portfolio_history: List[PortfolioSnapshot] = []
        self.daily_pnl: List[float] = []
        self.total_pnl = 0.0
        
        # Trading parameters
        self.commission_rate = 0.0003  # 0.03% brokerage
        self.min_trade_amount = 50.0   # Minimum ₹50 per trade
        self.max_position_size = 0.2   # Max 20% of portfolio per stock
        
        # Database setup
        self.portfolio_dir = Path("portfolio")
        self.portfolio_dir.mkdir(exist_ok=True)
        self.db_path = self.portfolio_dir / "portfolio.db"
        self._init_database()
        
        # Load existing data
        self._load_portfolio_state()
        
        self.logger.info(f"💰 Portfolio initialized with ₹{initial_capital:,.2f}")
    
    def _init_database(self):
        """Initialize portfolio database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Trades table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    trade_id TEXT PRIMARY KEY,
                    timestamp TEXT,
                    symbol TEXT,
                    action TEXT,
                    quantity INTEGER,
                    price REAL,
                    amount REAL,
                    commission REAL,
                    prediction_confidence REAL,
                    reasoning TEXT
                )
            ''')
            
            # Portfolio snapshots table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS portfolio_snapshots (
                    timestamp TEXT PRIMARY KEY,
                    cash_balance REAL,
                    total_value REAL,
                    total_pnl REAL,
                    day_pnl REAL,
                    trades_count INTEGER,
                    positions_json TEXT
                )
            ''')
            
            # Performance metrics table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    date TEXT PRIMARY KEY,
                    portfolio_value REAL,
                    daily_return REAL,
                    cumulative_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    profit_factor REAL
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error initializing portfolio database: {e}")
    
    def _load_portfolio_state(self):
        """Load existing portfolio state from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Load trades
            cursor.execute('SELECT * FROM trades ORDER BY timestamp')
            trade_rows = cursor.fetchall()
            
            for row in trade_rows:
                trade = Trade(
                    trade_id=row[0],
                    timestamp=datetime.fromisoformat(row[1]),
                    symbol=row[2],
                    action=row[3],
                    quantity=row[4],
                    price=row[5],
                    amount=row[6],
                    commission=row[7],
                    prediction_confidence=row[8],
                    reasoning=row[9]
                )
                self.trades.append(trade)
            
            # Rebuild portfolio state from trades
            self._rebuild_portfolio_from_trades()
            
            conn.close()
            
            self.logger.info(f"📊 Loaded {len(self.trades)} trades from database")
            
        except Exception as e:
            self.logger.error(f"Error loading portfolio state: {e}")
    
    def _rebuild_portfolio_from_trades(self):
        """Rebuild current portfolio state from trade history"""
        try:
            self.cash_balance = self.initial_capital
            self.positions = {}
            
            for trade in self.trades:
                if trade.action == "BUY":
                    self._process_buy_trade(trade, save_to_db=False)
                elif trade.action == "SELL":
                    self._process_sell_trade(trade, save_to_db=False)
            
        except Exception as e:
            self.logger.error(f"Error rebuilding portfolio: {e}")
    
    def can_buy(self, symbol: str, price: float, confidence: float) -> Tuple[bool, int, str]:
        """Check if we can buy a stock and return quantity"""
        try:
            # Calculate maximum affordable quantity
            available_cash = self.cash_balance
            max_position_value = self.get_portfolio_value() * self.max_position_size
            
            # Use smaller of available cash or max position size
            max_trade_value = min(available_cash * 0.95, max_position_value)  # 95% to leave some cash
            
            if max_trade_value < self.min_trade_amount:
                return False, 0, f"Insufficient funds (need ₹{self.min_trade_amount}, have ₹{max_trade_value:.2f})"
            
            # Calculate quantity (considering commission)
            commission = max_trade_value * self.commission_rate
            net_amount = max_trade_value - commission
            quantity = int(net_amount / price)
            
            if quantity <= 0:
                return False, 0, f"Cannot afford even 1 share at ₹{price:.2f}"
            
            # Adjust quantity based on confidence
            confidence_factor = max(0.3, min(1.0, confidence))  # 30% to 100%
            adjusted_quantity = max(1, int(quantity * confidence_factor))
            
            actual_amount = adjusted_quantity * price
            actual_commission = actual_amount * self.commission_rate
            total_cost = actual_amount + actual_commission
            
            if total_cost > available_cash:
                return False, 0, f"Total cost ₹{total_cost:.2f} exceeds available cash ₹{available_cash:.2f}"
            
            return True, adjusted_quantity, f"Can buy {adjusted_quantity} shares for ₹{actual_amount:.2f}"
            
        except Exception as e:
            self.logger.error(f"Error checking buy capability: {e}")
            return False, 0, "Error in buy check"
    
    def can_sell(self, symbol: str) -> Tuple[bool, int, str]:
        """Check if we can sell a stock and return quantity"""
        try:
            if symbol not in self.positions:
                return False, 0, f"No position in {symbol}"
            
            position = self.positions[symbol]
            if position.quantity <= 0:
                return False, 0, f"No shares to sell in {symbol}"
            
            return True, position.quantity, f"Can sell {position.quantity} shares"
            
        except Exception as e:
            self.logger.error(f"Error checking sell capability: {e}")
            return False, 0, "Error in sell check"
    
    def execute_buy(self, symbol: str, price: float, confidence: float, reasoning: str) -> Optional[Trade]:
        """Execute a buy order"""
        try:
            can_buy, quantity, message = self.can_buy(symbol, price, confidence)
            
            if not can_buy:
                self.logger.warning(f"❌ Cannot buy {symbol}: {message}")
                return None
            
            # Calculate trade details
            amount = quantity * price
            commission = amount * self.commission_rate
            total_cost = amount + commission
            
            # Create trade record
            trade = Trade(
                trade_id=f"BUY_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
                timestamp=datetime.now(),
                symbol=symbol,
                action="BUY",
                quantity=quantity,
                price=price,
                amount=amount,
                commission=commission,
                prediction_confidence=confidence,
                reasoning=reasoning
            )
            
            # Execute trade
            self._process_buy_trade(trade)
            
            self.logger.info(f"✅ BUY: {quantity} {symbol} @ ₹{price:.2f} = ₹{amount:.2f} (Commission: ₹{commission:.2f})")
            
            return trade
            
        except Exception as e:
            self.logger.error(f"Error executing buy order: {e}")
            return None
    
    def execute_sell(self, symbol: str, price: float, confidence: float, reasoning: str) -> Optional[Trade]:
        """Execute a sell order"""
        try:
            can_sell, quantity, message = self.can_sell(symbol)
            
            if not can_sell:
                self.logger.warning(f"❌ Cannot sell {symbol}: {message}")
                return None
            
            # Calculate trade details
            amount = quantity * price
            commission = amount * self.commission_rate
            net_proceeds = amount - commission
            
            # Create trade record
            trade = Trade(
                trade_id=f"SELL_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}",
                timestamp=datetime.now(),
                symbol=symbol,
                action="SELL",
                quantity=quantity,
                price=price,
                amount=amount,
                commission=commission,
                prediction_confidence=confidence,
                reasoning=reasoning
            )
            
            # Execute trade
            self._process_sell_trade(trade)
            
            self.logger.info(f"✅ SELL: {quantity} {symbol} @ ₹{price:.2f} = ₹{amount:.2f} (Commission: ₹{commission:.2f})")
            
            return trade
            
        except Exception as e:
            self.logger.error(f"Error executing sell order: {e}")
            return None
    
    def _process_buy_trade(self, trade: Trade, save_to_db: bool = True):
        """Process a buy trade"""
        try:
            total_cost = trade.amount + trade.commission
            
            # Update cash balance
            self.cash_balance -= total_cost
            
            # Update position
            if trade.symbol in self.positions:
                # Add to existing position
                position = self.positions[trade.symbol]
                total_quantity = position.quantity + trade.quantity
                total_cost_basis = (position.quantity * position.avg_buy_price) + trade.amount
                new_avg_price = total_cost_basis / total_quantity
                
                position.quantity = total_quantity
                position.avg_buy_price = new_avg_price
            else:
                # Create new position
                self.positions[trade.symbol] = Position(
                    symbol=trade.symbol,
                    quantity=trade.quantity,
                    avg_buy_price=trade.price,
                    current_price=trade.price,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0
                )
            
            # Add to trades list
            if save_to_db:
                self.trades.append(trade)
                self._save_trade_to_db(trade)
            
        except Exception as e:
            self.logger.error(f"Error processing buy trade: {e}")
    
    def _process_sell_trade(self, trade: Trade, save_to_db: bool = True):
        """Process a sell trade"""
        try:
            net_proceeds = trade.amount - trade.commission
            
            # Update cash balance
            self.cash_balance += net_proceeds
            
            # Update position
            if trade.symbol in self.positions:
                position = self.positions[trade.symbol]
                
                # Calculate realized P&L
                cost_basis = position.avg_buy_price * trade.quantity
                realized_pnl = trade.amount - cost_basis - trade.commission
                position.realized_pnl += realized_pnl
                self.total_pnl += realized_pnl
                
                # Update quantity
                position.quantity -= trade.quantity
                
                # Remove position if quantity is 0
                if position.quantity <= 0:
                    del self.positions[trade.symbol]
            
            # Add to trades list
            if save_to_db:
                self.trades.append(trade)
                self._save_trade_to_db(trade)
            
        except Exception as e:
            self.logger.error(f"Error processing sell trade: {e}")
    
    def _save_trade_to_db(self, trade: Trade):
        """Save trade to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO trades VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade.trade_id,
                trade.timestamp.isoformat(),
                trade.symbol,
                trade.action,
                trade.quantity,
                trade.price,
                trade.amount,
                trade.commission,
                trade.prediction_confidence,
                trade.reasoning
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error saving trade to database: {e}")
    
    def update_positions(self, current_prices: Dict[str, float]):
        """Update current prices and unrealized P&L for all positions"""
        try:
            for symbol, position in self.positions.items():
                if symbol in current_prices:
                    position.current_price = current_prices[symbol]
                    
                    # Calculate unrealized P&L
                    current_value = position.quantity * position.current_price
                    cost_basis = position.quantity * position.avg_buy_price
                    position.unrealized_pnl = current_value - cost_basis
            
        except Exception as e:
            self.logger.error(f"Error updating positions: {e}")
    
    def get_portfolio_value(self) -> float:
        """Get total portfolio value"""
        try:
            positions_value = sum(
                pos.quantity * pos.current_price 
                for pos in self.positions.values()
            )
            return self.cash_balance + positions_value
            
        except Exception as e:
            self.logger.error(f"Error calculating portfolio value: {e}")
            return self.cash_balance
    
    def get_total_pnl(self) -> float:
        """Get total P&L (realized + unrealized)"""
        try:
            realized_pnl = sum(pos.realized_pnl for pos in self.positions.values())
            unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
            return realized_pnl + unrealized_pnl
            
        except Exception as e:
            self.logger.error(f"Error calculating total P&L: {e}")
            return 0.0
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get comprehensive portfolio summary"""
        try:
            total_value = self.get_portfolio_value()
            total_pnl = self.get_total_pnl()
            total_return = (total_value - self.initial_capital) / self.initial_capital * 100
            
            # Calculate win rate
            profitable_trades = sum(1 for trade in self.trades if self._is_profitable_trade(trade))
            win_rate = (profitable_trades / len(self.trades) * 100) if self.trades else 0
            
            return {
                "initial_capital": self.initial_capital,
                "cash_balance": self.cash_balance,
                "positions_value": total_value - self.cash_balance,
                "total_value": total_value,
                "total_pnl": total_pnl,
                "total_return_pct": total_return,
                "total_trades": len(self.trades),
                "win_rate": win_rate,
                "positions": {symbol: asdict(pos) for symbol, pos in self.positions.items()},
                "recent_trades": [asdict(trade) for trade in self.trades[-5:]]  # Last 5 trades
            }
            
        except Exception as e:
            self.logger.error(f"Error generating portfolio summary: {e}")
            return {}
    
    def _is_profitable_trade(self, trade: Trade) -> bool:
        """Check if a trade was profitable (simplified)"""
        # This is a simplified check - in reality, we'd need to match buy/sell pairs
        return trade.action == "SELL" and trade.symbol in self.positions
    
    def save_portfolio_snapshot(self):
        """Save current portfolio state as snapshot"""
        try:
            snapshot = PortfolioSnapshot(
                timestamp=datetime.now(),
                cash_balance=self.cash_balance,
                total_value=self.get_portfolio_value(),
                positions=self.positions.copy(),
                total_pnl=self.get_total_pnl(),
                day_pnl=0.0,  # Would calculate based on previous day
                trades_count=len(self.trades)
            )
            
            self.portfolio_history.append(snapshot)
            
            # Save to database
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO portfolio_snapshots VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                snapshot.timestamp.isoformat(),
                snapshot.cash_balance,
                snapshot.total_value,
                snapshot.total_pnl,
                snapshot.day_pnl,
                snapshot.trades_count,
                json.dumps({symbol: asdict(pos) for symbol, pos in snapshot.positions.items()})
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Error saving portfolio snapshot: {e}")

# Global portfolio manager instance
portfolio_manager = PortfolioManager(initial_capital=1000.0)
