"""
Memory Visualization and Analysis Tools
Provides insights into the AI agent's learning progress and memory patterns
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from pathlib import Path
from collections import Counter, defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from textblob import TextBlob

from memory.persistent_memory import persistent_memory

class MemoryVisualizer:
    """
    Visualize and analyze the AI agent's persistent memory
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.memory = persistent_memory
        
    def generate_memory_report(self) -> Dict[str, Any]:
        """Generate comprehensive memory analysis report"""
        try:
            report = {
                "timestamp": datetime.now().isoformat(),
                "summary": self._generate_summary(),
                "learning_progress": self._analyze_learning_progress(),
                "pattern_analysis": self._analyze_patterns(),
                "sector_insights": self._analyze_sector_performance(),
                "nlp_insights": self._analyze_nlp_patterns(),
                "confidence_calibration": self._analyze_confidence_calibration(),
                "temporal_patterns": self._analyze_temporal_patterns(),
                "recommendations": self._generate_recommendations()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating memory report: {e}")
            return {"error": str(e)}
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate high-level memory summary"""
        try:
            summary = self.memory.get_memory_summary()
            
            # Add additional insights
            if summary.get("total_experiences", 0) > 0:
                experiences = self.memory.experiences
                
                # Recent performance trend
                if len(experiences) >= 20:
                    recent_accuracy = np.mean([exp.accuracy_score for exp in experiences[-10:]])
                    older_accuracy = np.mean([exp.accuracy_score for exp in experiences[-20:-10]])
                    trend = "improving" if recent_accuracy > older_accuracy else "declining"
                    summary["performance_trend"] = trend
                    summary["trend_magnitude"] = abs(recent_accuracy - older_accuracy)
                
                # Learning velocity
                if len(experiences) >= 50:
                    early_accuracy = np.mean([exp.accuracy_score for exp in experiences[:25]])
                    recent_accuracy = np.mean([exp.accuracy_score for exp in experiences[-25:]])
                    summary["learning_velocity"] = recent_accuracy - early_accuracy
                
                # Most active symbols
                symbol_counts = Counter([exp.symbol for exp in experiences])
                summary["most_traded_symbols"] = dict(symbol_counts.most_common(5))
                
                # Best performing time periods
                hourly_performance = defaultdict(list)
                for exp in experiences:
                    hour = exp.timestamp.hour
                    hourly_performance[hour].append(exp.accuracy_score)
                
                best_hours = []
                for hour, accuracies in hourly_performance.items():
                    if len(accuracies) >= 3:  # Minimum sample size
                        avg_accuracy = np.mean(accuracies)
                        best_hours.append((hour, avg_accuracy))
                
                best_hours.sort(key=lambda x: x[1], reverse=True)
                summary["best_trading_hours"] = best_hours[:3]
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Error generating summary: {e}")
            return {}
    
    def _analyze_learning_progress(self) -> Dict[str, Any]:
        """Analyze learning progress over time"""
        try:
            experiences = self.memory.experiences
            if len(experiences) < 10:
                return {"status": "insufficient_data"}
            
            # Group experiences by time windows
            time_windows = []
            window_size = max(10, len(experiences) // 10)  # 10 windows
            
            for i in range(0, len(experiences), window_size):
                window_experiences = experiences[i:i+window_size]
                if len(window_experiences) >= 5:
                    window_data = {
                        "window_start": window_experiences[0].timestamp.isoformat(),
                        "window_end": window_experiences[-1].timestamp.isoformat(),
                        "experience_count": len(window_experiences),
                        "avg_accuracy": np.mean([exp.accuracy_score for exp in window_experiences]),
                        "avg_confidence": np.mean([exp.confidence_before for exp in window_experiences]),
                        "avg_reward": np.mean([exp.reward for exp in window_experiences]),
                        "confidence_calibration": self._calculate_confidence_calibration(window_experiences)
                    }
                    time_windows.append(window_data)
            
            # Calculate learning metrics
            if len(time_windows) >= 3:
                first_window = time_windows[0]
                last_window = time_windows[-1]
                
                accuracy_improvement = last_window["avg_accuracy"] - first_window["avg_accuracy"]
                confidence_improvement = last_window["confidence_calibration"] - first_window["confidence_calibration"]
                
                return {
                    "time_windows": time_windows,
                    "accuracy_improvement": accuracy_improvement,
                    "confidence_improvement": confidence_improvement,
                    "learning_rate": accuracy_improvement / len(time_windows),
                    "consistency": np.std([w["avg_accuracy"] for w in time_windows])
                }
            
            return {"time_windows": time_windows}
            
        except Exception as e:
            self.logger.error(f"Error analyzing learning progress: {e}")
            return {"error": str(e)}
    
    def _calculate_confidence_calibration(self, experiences: List) -> float:
        """Calculate how well confidence matches actual accuracy"""
        try:
            if not experiences:
                return 0.0
            
            confidence_errors = [
                abs(exp.confidence_before - exp.accuracy_score) 
                for exp in experiences
            ]
            
            # Return inverse of average error (better calibration = higher score)
            avg_error = np.mean(confidence_errors)
            return max(0, 1 - avg_error)
            
        except Exception:
            return 0.0
    
    def _analyze_patterns(self) -> Dict[str, Any]:
        """Analyze discovered patterns"""
        try:
            patterns = self.memory.patterns
            
            if not patterns:
                return {"status": "no_patterns"}
            
            pattern_analysis = {
                "total_patterns": len(patterns),
                "pattern_types": Counter([p.pattern_type for p in patterns.values()]),
                "high_confidence_patterns": [],
                "recent_patterns": [],
                "pattern_evolution": {}
            }
            
            # High confidence patterns
            for pattern in patterns.values():
                if pattern.confidence > 0.7 and pattern.success_rate > 0.6:
                    pattern_analysis["high_confidence_patterns"].append({
                        "id": pattern.pattern_id,
                        "type": pattern.pattern_type,
                        "description": pattern.description,
                        "success_rate": pattern.success_rate,
                        "confidence": pattern.confidence
                    })
            
            # Recent patterns (last 7 days)
            recent_cutoff = datetime.now() - timedelta(days=7)
            for pattern in patterns.values():
                if pattern.discovered_date > recent_cutoff:
                    pattern_analysis["recent_patterns"].append({
                        "id": pattern.pattern_id,
                        "description": pattern.description,
                        "discovered": pattern.discovered_date.isoformat()
                    })
            
            # Pattern evolution (how patterns change over time)
            pattern_types = defaultdict(list)
            for pattern in patterns.values():
                pattern_types[pattern.pattern_type].append({
                    "discovered": pattern.discovered_date,
                    "success_rate": pattern.success_rate,
                    "confidence": pattern.confidence
                })
            
            for pattern_type, pattern_list in pattern_types.items():
                pattern_list.sort(key=lambda x: x["discovered"])
                if len(pattern_list) >= 2:
                    first = pattern_list[0]
                    last = pattern_list[-1]
                    pattern_analysis["pattern_evolution"][pattern_type] = {
                        "success_rate_change": last["success_rate"] - first["success_rate"],
                        "confidence_change": last["confidence"] - first["confidence"],
                        "pattern_count": len(pattern_list)
                    }
            
            return pattern_analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing patterns: {e}")
            return {"error": str(e)}
    
    def _analyze_sector_performance(self) -> Dict[str, Any]:
        """Analyze performance by sector"""
        try:
            experiences = self.memory.experiences
            if not experiences:
                return {"status": "no_data"}
            
            sector_data = defaultdict(lambda: {
                "experiences": [],
                "accuracy_scores": [],
                "rewards": [],
                "confidence_scores": []
            })
            
            # Group by sector
            for exp in experiences:
                sector = exp.sector
                sector_data[sector]["experiences"].append(exp)
                sector_data[sector]["accuracy_scores"].append(exp.accuracy_score)
                sector_data[sector]["rewards"].append(exp.reward)
                sector_data[sector]["confidence_scores"].append(exp.confidence_before)
            
            # Calculate sector metrics
            sector_analysis = {}
            for sector, data in sector_data.items():
                if len(data["experiences"]) >= 3:  # Minimum sample size
                    sector_analysis[sector] = {
                        "experience_count": len(data["experiences"]),
                        "avg_accuracy": np.mean(data["accuracy_scores"]),
                        "accuracy_std": np.std(data["accuracy_scores"]),
                        "avg_reward": np.mean(data["rewards"]),
                        "avg_confidence": np.mean(data["confidence_scores"]),
                        "best_accuracy": max(data["accuracy_scores"]),
                        "worst_accuracy": min(data["accuracy_scores"]),
                        "consistency": 1 - np.std(data["accuracy_scores"]),  # Higher = more consistent
                        "recent_trend": self._calculate_recent_trend(data["accuracy_scores"])
                    }
            
            # Rank sectors
            if sector_analysis:
                sorted_sectors = sorted(
                    sector_analysis.items(),
                    key=lambda x: x[1]["avg_accuracy"],
                    reverse=True
                )
                
                return {
                    "sector_performance": sector_analysis,
                    "best_sectors": [s[0] for s in sorted_sectors[:3]],
                    "worst_sectors": [s[0] for s in sorted_sectors[-3:]],
                    "most_consistent": max(sector_analysis.items(), key=lambda x: x[1]["consistency"])[0],
                    "most_volatile": min(sector_analysis.items(), key=lambda x: x[1]["consistency"])[0]
                }
            
            return {"sector_performance": sector_analysis}
            
        except Exception as e:
            self.logger.error(f"Error analyzing sector performance: {e}")
            return {"error": str(e)}
    
    def _calculate_recent_trend(self, scores: List[float]) -> str:
        """Calculate trend direction for recent scores"""
        try:
            if len(scores) < 6:
                return "insufficient_data"
            
            recent_half = scores[len(scores)//2:]
            older_half = scores[:len(scores)//2]
            
            recent_avg = np.mean(recent_half)
            older_avg = np.mean(older_half)
            
            diff = recent_avg - older_avg
            
            if diff > 0.1:
                return "strongly_improving"
            elif diff > 0.05:
                return "improving"
            elif diff < -0.1:
                return "strongly_declining"
            elif diff < -0.05:
                return "declining"
            else:
                return "stable"
                
        except Exception:
            return "unknown"
    
    def _analyze_nlp_patterns(self) -> Dict[str, Any]:
        """Analyze NLP-derived insights"""
        try:
            if not hasattr(self.memory, 'knowledge_base') or 'nlp_insights' not in self.memory.knowledge_base:
                return {"status": "no_nlp_data"}
            
            nlp_data = self.memory.knowledge_base['nlp_insights']
            
            analysis = {
                "common_keywords": dict(Counter(nlp_data.get('common_keywords', {})).most_common(10)),
                "sentiment_patterns": nlp_data.get('sentiment_patterns', {}),
                "success_factors": nlp_data.get('success_factors', [])[-10:],  # Recent 10
                "failure_factors": nlp_data.get('failure_factors', [])[-10:],  # Recent 10
                "learning_themes": self._extract_learning_themes()
            }
            
            # Analyze sentiment correlation with performance
            sentiment_data = nlp_data.get('sentiment_patterns', {})
            if sentiment_data:
                analysis["sentiment_performance"] = {
                    sentiment: {
                        "accuracy": data.get('avg_accuracy', 0),
                        "count": data.get('count', 0)
                    }
                    for sentiment, data in sentiment_data.items()
                }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing NLP patterns: {e}")
            return {"error": str(e)}
    
    def _extract_learning_themes(self) -> List[str]:
        """Extract common learning themes from lessons"""
        try:
            experiences = self.memory.experiences
            if not experiences:
                return []
            
            # Collect all lessons
            lessons = [exp.lesson_learned for exp in experiences[-100:]]  # Recent 100
            
            # Extract common themes using simple keyword analysis
            theme_keywords = {
                "overconfidence": ["overconfident", "too confident", "confidence was high"],
                "volatility_challenges": ["high volatility", "volatile", "uncertainty"],
                "sector_strength": ["strong sector", "good sector", "sector strength"],
                "technical_analysis": ["technical", "indicators", "signals"],
                "timing_issues": ["timing", "early", "late", "premature"],
                "market_conditions": ["market", "conditions", "environment"]
            }
            
            theme_counts = {}
            for theme, keywords in theme_keywords.items():
                count = 0
                for lesson in lessons:
                    lesson_lower = lesson.lower()
                    if any(keyword in lesson_lower for keyword in keywords):
                        count += 1
                theme_counts[theme] = count
            
            # Return themes sorted by frequency
            sorted_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)
            return [theme for theme, count in sorted_themes if count > 0]
            
        except Exception as e:
            self.logger.error(f"Error extracting learning themes: {e}")
            return []
    
    def _analyze_confidence_calibration(self) -> Dict[str, Any]:
        """Analyze how well confidence matches actual performance"""
        try:
            experiences = self.memory.experiences
            if len(experiences) < 10:
                return {"status": "insufficient_data"}
            
            # Group by confidence ranges
            confidence_ranges = {
                "very_high": (0.8, 1.0),
                "high": (0.6, 0.8),
                "medium": (0.4, 0.6),
                "low": (0.2, 0.4),
                "very_low": (0.0, 0.2)
            }
            
            calibration_data = {}
            for range_name, (min_conf, max_conf) in confidence_ranges.items():
                range_experiences = [
                    exp for exp in experiences
                    if min_conf <= exp.confidence_before < max_conf
                ]
                
                if range_experiences:
                    avg_accuracy = np.mean([exp.accuracy_score for exp in range_experiences])
                    avg_confidence = np.mean([exp.confidence_before for exp in range_experiences])
                    
                    calibration_data[range_name] = {
                        "count": len(range_experiences),
                        "avg_confidence": avg_confidence,
                        "avg_accuracy": avg_accuracy,
                        "calibration_error": abs(avg_confidence - avg_accuracy),
                        "overconfident": avg_confidence > avg_accuracy
                    }
            
            # Overall calibration score
            overall_error = np.mean([
                abs(exp.confidence_before - exp.accuracy_score)
                for exp in experiences
            ])
            
            return {
                "calibration_by_range": calibration_data,
                "overall_calibration_error": overall_error,
                "calibration_score": max(0, 1 - overall_error),  # 1 = perfect, 0 = worst
                "is_well_calibrated": overall_error < 0.2
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing confidence calibration: {e}")
            return {"error": str(e)}
    
    def _analyze_temporal_patterns(self) -> Dict[str, Any]:
        """Analyze time-based patterns"""
        try:
            experiences = self.memory.experiences
            if len(experiences) < 20:
                return {"status": "insufficient_data"}
            
            # Hour-based analysis
            hourly_data = defaultdict(list)
            daily_data = defaultdict(list)
            
            for exp in experiences:
                hour = exp.timestamp.hour
                day = exp.timestamp.weekday()  # 0 = Monday
                
                hourly_data[hour].append(exp.accuracy_score)
                daily_data[day].append(exp.accuracy_score)
            
            # Calculate hourly performance
            hourly_performance = {}
            for hour, accuracies in hourly_data.items():
                if len(accuracies) >= 3:
                    hourly_performance[hour] = {
                        "avg_accuracy": np.mean(accuracies),
                        "count": len(accuracies),
                        "std": np.std(accuracies)
                    }
            
            # Calculate daily performance
            day_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
            daily_performance = {}
            for day, accuracies in daily_data.items():
                if len(accuracies) >= 3:
                    daily_performance[day_names[day]] = {
                        "avg_accuracy": np.mean(accuracies),
                        "count": len(accuracies),
                        "std": np.std(accuracies)
                    }
            
            # Find best/worst times
            best_hour = max(hourly_performance.items(), key=lambda x: x[1]["avg_accuracy"]) if hourly_performance else None
            worst_hour = min(hourly_performance.items(), key=lambda x: x[1]["avg_accuracy"]) if hourly_performance else None
            
            return {
                "hourly_performance": hourly_performance,
                "daily_performance": daily_performance,
                "best_hour": best_hour,
                "worst_hour": worst_hour,
                "trading_session_analysis": self._analyze_trading_sessions(hourly_performance)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing temporal patterns: {e}")
            return {"error": str(e)}
    
    def _analyze_trading_sessions(self, hourly_performance: Dict) -> Dict[str, Any]:
        """Analyze performance by trading session"""
        try:
            sessions = {
                "pre_market": list(range(9, 10)),  # 9-10 AM
                "morning": list(range(10, 12)),    # 10-12 PM
                "midday": list(range(12, 14)),     # 12-2 PM
                "afternoon": list(range(14, 16))   # 2-4 PM
            }
            
            session_performance = {}
            for session_name, hours in sessions.items():
                session_accuracies = []
                session_counts = []
                
                for hour in hours:
                    if hour in hourly_performance:
                        hour_data = hourly_performance[hour]
                        # Weight by count
                        session_accuracies.extend([hour_data["avg_accuracy"]] * hour_data["count"])
                        session_counts.append(hour_data["count"])
                
                if session_accuracies:
                    session_performance[session_name] = {
                        "avg_accuracy": np.mean(session_accuracies),
                        "total_trades": sum(session_counts),
                        "consistency": 1 - np.std(session_accuracies) if len(session_accuracies) > 1 else 1
                    }
            
            return session_performance
            
        except Exception:
            return {}
    
    def _generate_recommendations(self) -> List[str]:
        """Generate actionable recommendations based on memory analysis"""
        try:
            recommendations = []
            
            # Get recent performance
            experiences = self.memory.experiences
            if len(experiences) < 10:
                recommendations.append("Continue trading to build sufficient memory for analysis")
                return recommendations
            
            recent_accuracy = np.mean([exp.accuracy_score for exp in experiences[-20:]])
            overall_accuracy = np.mean([exp.accuracy_score for exp in experiences])
            
            # Performance recommendations
            if recent_accuracy < 0.5:
                recommendations.append("Recent performance is below 50% - consider reviewing strategy")
            elif recent_accuracy > 0.7:
                recommendations.append("Excellent recent performance - current strategy is working well")
            
            # Confidence calibration recommendations
            confidence_errors = [abs(exp.confidence_before - exp.accuracy_score) for exp in experiences[-50:]]
            avg_error = np.mean(confidence_errors)
            
            if avg_error > 0.3:
                recommendations.append("Confidence calibration needs improvement - review confidence assessment")
            elif avg_error < 0.15:
                recommendations.append("Good confidence calibration - trust your confidence levels")
            
            # Sector recommendations
            memory_summary = self.memory.get_memory_summary()
            if 'sector_performance' in memory_summary:
                sector_perf = memory_summary['sector_performance']
                best_sectors = sorted(sector_perf.items(), key=lambda x: x[1].get('accuracy', 0), reverse=True)
                
                if best_sectors:
                    best_sector = best_sectors[0][0]
                    recommendations.append(f"Focus more on {best_sector} sector - showing strong performance")
                
                if len(best_sectors) > 1:
                    worst_sector = best_sectors[-1][0]
                    worst_accuracy = best_sectors[-1][1].get('accuracy', 0)
                    if worst_accuracy < 0.4:
                        recommendations.append(f"Avoid or be cautious with {worst_sector} sector")
            
            # Pattern-based recommendations
            patterns = self.memory.patterns
            high_success_patterns = [p for p in patterns.values() if p.success_rate > 0.7]
            
            if high_success_patterns:
                recommendations.append(f"Leverage {len(high_success_patterns)} high-success patterns identified")
            
            # Temporal recommendations
            if len(experiences) >= 30:
                hourly_data = defaultdict(list)
                for exp in experiences:
                    hourly_data[exp.timestamp.hour].append(exp.accuracy_score)
                
                best_hours = []
                for hour, accuracies in hourly_data.items():
                    if len(accuracies) >= 3 and np.mean(accuracies) > 0.7:
                        best_hours.append(hour)
                
                if best_hours:
                    recommendations.append(f"Focus trading during hours: {sorted(best_hours)} (historically strong)")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations - check logs"]
    
    def save_memory_report(self, filename: Optional[str] = None) -> str:
        """Save memory report to file"""
        try:
            if filename is None:
                filename = f"memory_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            report = self.generate_memory_report()
            
            # Ensure reports directory exists
            reports_dir = Path("reports")
            reports_dir.mkdir(exist_ok=True)
            
            filepath = reports_dir / filename
            
            with open(filepath, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Memory report saved to {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"Error saving memory report: {e}")
            return ""

# Global visualizer instance
memory_visualizer = MemoryVisualizer()
