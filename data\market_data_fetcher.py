"""
Advanced Multi-Source Market Data Fetcher
Optimized for Indian Stock Market with real-time capabilities
"""

import asyncio
import aiohttp
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from dataclasses import dataclass
import time
import json
import os
from concurrent.futures import ThreadPoolExecutor
import requests

from config.indian_stocks import NIFTY_50, SECTORS, INDICES
from config.settings import settings

@dataclass
class MarketData:
    """Market data container"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    timeframe: str
    source: str

class MarketDataFetcher:
    """Advanced market data fetcher with multiple sources and caching"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cache = {}
        self.cache_timestamps = {}
        self.session = None
        self.alpha_vantage_key = settings.alpha_vantage_api_key
        
        # Rate limiting
        self.last_request_time = {}
        self.min_request_interval = 1.0  # seconds between requests
        
        # Initialize session (will be done lazily)
        self._session_initialized = False
    
    async def _init_session(self):
        """Initialize aiohttp session"""
        if not self._session_initialized:
            self.session = aiohttp.ClientSession()
            self._session_initialized = True
    
    def _is_cache_valid(self, key: str) -> bool:
        """Check if cached data is still valid"""
        if key not in self.cache_timestamps:
            return False
        
        cache_age = time.time() - self.cache_timestamps[key]
        return cache_age < settings.data.cache_duration
    
    def _rate_limit(self, source: str):
        """Implement rate limiting"""
        current_time = time.time()
        if source in self.last_request_time:
            time_since_last = current_time - self.last_request_time[source]
            if time_since_last < self.min_request_interval:
                time.sleep(self.min_request_interval - time_since_last)
        
        self.last_request_time[source] = time.time()
    
    async def fetch_realtime_data(self, symbol: str) -> Optional[MarketData]:
        """Fetch real-time data for a symbol"""
        try:
            # Initialize session if needed
            await self._init_session()
            # Try multiple sources in order of preference
            sources = [
                self._fetch_yfinance_realtime,
                self._fetch_alpha_vantage_realtime,
                self._fetch_fallback_data
            ]
            
            for source_func in sources:
                try:
                    data = await source_func(symbol)
                    if data:
                        return data
                except Exception as e:
                    self.logger.warning(f"Source failed for {symbol}: {e}")
                    continue
            
            self.logger.error(f"All sources failed for {symbol}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching real-time data for {symbol}: {e}")
            return None
    
    async def _fetch_yfinance_realtime(self, symbol: str) -> Optional[MarketData]:
        """Fetch real-time data using yfinance"""
        try:
            self._rate_limit("yfinance")
            
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="1d", interval="1m")
            
            if hist.empty:
                return None
            
            latest = hist.iloc[-1]
            
            return MarketData(
                symbol=symbol,
                timestamp=datetime.now(),
                open=float(latest['Open']),
                high=float(latest['High']),
                low=float(latest['Low']),
                close=float(latest['Close']),
                volume=int(latest['Volume']),
                timeframe="1m",
                source="yfinance"
            )
            
        except Exception as e:
            self.logger.error(f"YFinance error for {symbol}: {e}")
            return None
    
    async def _fetch_alpha_vantage_realtime(self, symbol: str) -> Optional[MarketData]:
        """Fetch real-time data using Alpha Vantage"""
        if not self.alpha_vantage_key:
            return None
        
        try:
            self._rate_limit("alpha_vantage")
            
            # Convert NSE symbol format
            av_symbol = symbol.replace(".NS", "")
            
            url = f"https://www.alphavantage.co/query"
            params = {
                "function": "GLOBAL_QUOTE",
                "symbol": av_symbol,
                "apikey": self.alpha_vantage_key
            }
            
            if not self.session:
                await self._init_session()

            async with self.session.get(url, params=params) as response:
                data = await response.json()
                
                if "Global Quote" not in data:
                    return None
                
                quote = data["Global Quote"]
                
                return MarketData(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    open=float(quote["02. open"]),
                    high=float(quote["03. high"]),
                    low=float(quote["04. low"]),
                    close=float(quote["05. price"]),
                    volume=int(quote["06. volume"]),
                    timeframe="1d",
                    source="alpha_vantage"
                )
                
        except Exception as e:
            self.logger.error(f"Alpha Vantage error for {symbol}: {e}")
            return None
    
    async def _fetch_fallback_data(self, symbol: str) -> Optional[MarketData]:
        """Fallback data source using cached historical data"""
        try:
            # Use last known data with current timestamp
            cache_key = f"{symbol}_fallback"
            
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]
            
            # Fetch recent historical data as fallback
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="2d", interval="1h")
            
            if hist.empty:
                return None
            
            latest = hist.iloc[-1]
            
            data = MarketData(
                symbol=symbol,
                timestamp=datetime.now(),
                open=float(latest['Open']),
                high=float(latest['High']),
                low=float(latest['Low']),
                close=float(latest['Close']),
                volume=int(latest['Volume']),
                timeframe="1h",
                source="fallback"
            )
            
            # Cache the fallback data
            self.cache[cache_key] = data
            self.cache_timestamps[cache_key] = time.time()
            
            return data
            
        except Exception as e:
            self.logger.error(f"Fallback data error for {symbol}: {e}")
            return None
    
    async def fetch_historical_data(self, symbol: str, period: str = "30d", 
                                  interval: str = "1h") -> pd.DataFrame:
        """Fetch historical data for training"""
        try:
            cache_key = f"{symbol}_{period}_{interval}"
            
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]
            
            self._rate_limit("yfinance")
            
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period, interval=interval)
            
            if hist.empty:
                self.logger.warning(f"No historical data for {symbol}")
                return pd.DataFrame()
            
            # Add technical indicators
            hist = self._add_technical_indicators(hist)
            
            # Cache the data
            self.cache[cache_key] = hist
            self.cache_timestamps[cache_key] = time.time()
            
            return hist
            
        except Exception as e:
            self.logger.error(f"Error fetching historical data for {symbol}: {e}")
            return pd.DataFrame()
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to the dataframe"""
        try:
            # Simple Moving Averages
            df['SMA_20'] = df['Close'].rolling(window=20).mean()
            df['SMA_50'] = df['Close'].rolling(window=50).mean()
            
            # Exponential Moving Average
            df['EMA_12'] = df['Close'].ewm(span=12).mean()
            
            # RSI
            delta = df['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # MACD
            ema_12 = df['Close'].ewm(span=12).mean()
            ema_26 = df['Close'].ewm(span=26).mean()
            df['MACD'] = ema_12 - ema_26
            df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
            
            # Bollinger Bands
            df['BB_Middle'] = df['Close'].rolling(window=20).mean()
            bb_std = df['Close'].rolling(window=20).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
            
            # Volume indicators
            df['Volume_SMA'] = df['Volume'].rolling(window=20).mean()
            df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
            
            return df
            
        except Exception as e:
            self.logger.error(f"Error adding technical indicators: {e}")
            return df
    
    async def fetch_sector_data(self, sector: str) -> Dict[str, MarketData]:
        """Fetch data for all stocks in a sector"""
        if sector not in SECTORS:
            self.logger.error(f"Unknown sector: {sector}")
            return {}
        
        stocks = SECTORS[sector]["stocks"]
        tasks = [self.fetch_realtime_data(stock) for stock in stocks]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        sector_data = {}
        for stock, result in zip(stocks, results):
            if isinstance(result, MarketData):
                sector_data[stock] = result
            else:
                self.logger.warning(f"Failed to fetch data for {stock}: {result}")
        
        return sector_data
    
    async def fetch_market_indices(self) -> Dict[str, MarketData]:
        """Fetch data for major market indices"""
        tasks = [self.fetch_realtime_data(index) for index in INDICES.values()]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        indices_data = {}
        for index_name, result in zip(INDICES.keys(), results):
            if isinstance(result, MarketData):
                indices_data[index_name] = result
        
        return indices_data
    
    def get_today_data_for_training(self, symbols: List[str]) -> Dict[str, pd.DataFrame]:
        """Get today's data for training (since market is closed)"""
        training_data = {}
        
        for symbol in symbols:
            try:
                # Get today's data with higher frequency for training
                ticker = yf.Ticker(symbol)
                
                # Get last 2 days of 1-minute data
                hist = ticker.history(period="2d", interval="1m")
                
                if not hist.empty:
                    # Add technical indicators
                    hist = self._add_technical_indicators(hist)
                    training_data[symbol] = hist
                    
                    self.logger.info(f"Loaded {len(hist)} data points for {symbol}")
                else:
                    self.logger.warning(f"No data available for {symbol}")
                    
            except Exception as e:
                self.logger.error(f"Error loading training data for {symbol}: {e}")
        
        return training_data
    
    async def close(self):
        """Close the session"""
        if self.session:
            await self.session.close()

# Global instance (created lazily)
market_data_fetcher = None

def get_market_data_fetcher():
    """Get or create the global market data fetcher instance"""
    global market_data_fetcher
    if market_data_fetcher is None:
        market_data_fetcher = MarketDataFetcher()
    return market_data_fetcher
