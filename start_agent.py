#!/usr/bin/env python3
"""
Quick Start Script for Revolutionary Trading AI Agent
"""

import sys
import subprocess
import os
from colorama import init, Fore, Style

# Initialize colorama
init()

def print_banner():
    """Print the startup banner"""
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}🚀 REVOLUTIONARY INTRADAY TRADING AI AGENT{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Zero-Shot Learning | Real-Time Predictions | Continuous Improvement{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = [
        'torch', 'numpy', 'pandas', 'yfinance', 'ta', 'colorama', 'aiohttp'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"{Fore.RED}❌ Missing required packages: {', '.join(missing_packages)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Installing missing packages...{Style.RESET_ALL}")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install"
            ] + missing_packages)
            print(f"{Fore.GREEN}✅ All packages installed successfully!{Style.RESET_ALL}")
        except subprocess.CalledProcessError:
            print(f"{Fore.RED}❌ Failed to install packages. Please run: pip install {' '.join(missing_packages)}{Style.RESET_ALL}")
            return False
    
    return True

def show_menu():
    """Show the main menu"""
    print(f"\n{Fore.CYAN}📋 CHOOSE YOUR OPTION:{Style.RESET_ALL}")
    print(f"1. 🎬 Run Demo (See AI in action)")
    print(f"2. 🚀 Start Live Trading Agent")
    print(f"3. 🏭 Focus on IT Sector")
    print(f"4. 🏦 Focus on Banking Sector")
    print(f"5. 💊 Focus on Pharma Sector")
    print(f"6. 📊 Custom Symbols")
    print(f"7. ❓ Help & Documentation")
    print(f"8. 🚪 Exit")

def run_demo():
    """Run the demo"""
    print(f"\n{Fore.YELLOW}🎬 Starting Demo...{Style.RESET_ALL}")
    try:
        subprocess.run([sys.executable, "demo.py"], check=True)
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Demo failed to run{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Demo stopped by user{Style.RESET_ALL}")

def start_live_agent(args=None):
    """Start the live trading agent"""
    cmd = [sys.executable, "main.py"]
    if args:
        cmd.extend(args)
    
    print(f"\n{Fore.YELLOW}🚀 Starting Live Trading Agent...{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Command: {' '.join(cmd)}{Style.RESET_ALL}")
    print(f"{Fore.GREEN}Press Ctrl+C to stop the agent{Style.RESET_ALL}\n")
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError:
        print(f"{Fore.RED}❌ Agent failed to start{Style.RESET_ALL}")
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}Agent stopped by user{Style.RESET_ALL}")

def get_custom_symbols():
    """Get custom symbols from user"""
    print(f"\n{Fore.CYAN}📊 Enter stock symbols (NSE format, e.g., RELIANCE.NS TCS.NS):{Style.RESET_ALL}")
    symbols_input = input("Symbols: ").strip()
    
    if symbols_input:
        symbols = symbols_input.split()
        return ["--symbols"] + symbols
    else:
        print(f"{Fore.YELLOW}No symbols entered, using default NIFTY 50{Style.RESET_ALL}")
        return None

def show_help():
    """Show help and documentation"""
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}📚 HELP & DOCUMENTATION{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    print(f"\n{Fore.GREEN}🎯 What this AI does:{Style.RESET_ALL}")
    print("• Makes real-time predictions on Indian stock market")
    print("• Learns from its mistakes using reinforcement learning")
    print("• Adapts to different market sectors automatically")
    print("• Uses advanced technical analysis and AI reasoning")
    print("• Optimized for 4GB RAM and older computers")
    
    print(f"\n{Fore.GREEN}🚀 Quick Start:{Style.RESET_ALL}")
    print("1. Run Demo first to see how it works")
    print("2. Start with a specific sector (IT, Banking, Pharma)")
    print("3. Monitor the real-time predictions and learning")
    print("4. Check logs/ folder for detailed performance data")
    
    print(f"\n{Fore.GREEN}📊 Supported Markets:{Style.RESET_ALL}")
    print("• NSE (National Stock Exchange)")
    print("• BSE (Bombay Stock Exchange)")
    print("• Real-time data during market hours (9:15 AM - 3:30 PM IST)")
    
    print(f"\n{Fore.GREEN}🛡️  Safety:{Style.RESET_ALL}")
    print("• Paper trading only - no real money involved")
    print("• All predictions are tracked and analyzed")
    print("• Continuous learning improves accuracy over time")
    
    print(f"\n{Fore.GREEN}📁 Files Created:{Style.RESET_ALL}")
    print("• logs/ - Detailed trading logs")
    print("• models/ - Saved AI models")
    print("• data/ - Market data cache")
    print("• cache/ - Temporary files")
    
    print(f"\n{Fore.YELLOW}💡 Pro Tips:{Style.RESET_ALL}")
    print("• Let the agent run for at least 30 minutes to see learning")
    print("• Check accuracy rates - they improve over time")
    print("• Different sectors have different volatility patterns")
    print("• The agent adapts its strategy based on market conditions")

def main():
    """Main function"""
    print_banner()
    
    # Check dependencies
    if not check_dependencies():
        return
    
    print(f"\n{Fore.GREEN}✅ All dependencies are ready!{Style.RESET_ALL}")
    
    while True:
        show_menu()
        
        try:
            choice = input(f"\n{Fore.CYAN}Enter your choice (1-8): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                run_demo()
            
            elif choice == "2":
                start_live_agent()
            
            elif choice == "3":
                start_live_agent(["--sector", "IT"])
            
            elif choice == "4":
                start_live_agent(["--sector", "BANKING"])
            
            elif choice == "5":
                start_live_agent(["--sector", "PHARMA"])
            
            elif choice == "6":
                args = get_custom_symbols()
                start_live_agent(args)
            
            elif choice == "7":
                show_help()
            
            elif choice == "8":
                print(f"\n{Fore.YELLOW}👋 Goodbye! Thanks for using the Revolutionary Trading AI!{Style.RESET_ALL}")
                break
            
            else:
                print(f"{Fore.RED}❌ Invalid choice. Please enter 1-8.{Style.RESET_ALL}")
        
        except KeyboardInterrupt:
            print(f"\n\n{Fore.YELLOW}👋 Goodbye! Thanks for using the Revolutionary Trading AI!{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"{Fore.RED}❌ Error: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
