"""
Revolutionary Zero-Shot Meta-Learning RL Agent
Learns from live market predictions without any pre-training data
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from dataclasses import dataclass
from collections import deque
import random
import json
from datetime import datetime
import asyncio

from environment.live_prediction_env import PredictionAction, live_env
from config.settings import settings
from tools.market_analyzer import MarketAnalyzer

@dataclass
class MetaLearningExperience:
    """Experience for meta-learning"""
    task_context: Dict[str, Any]  # Market context (sector, volatility, etc.)
    state: np.ndarray
    action: int
    reward: float
    next_state: np.ndarray
    done: bool
    meta_features: Dict[str, float]  # High-level market features

class AdaptiveNeuralNetwork(nn.Module):
    """
    Memory-efficient neural network with adaptive capabilities
    Optimized for 4GB RAM systems
    """
    
    def __init__(self, input_size: int, hidden_sizes: List[int], output_size: int):
        super().__init__()
        
        # Main network layers
        layers = []
        prev_size = input_size
        
        for hidden_size in hidden_sizes:
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            prev_size = hidden_size
        
        layers.append(nn.Linear(prev_size, output_size))
        self.network = nn.Sequential(*layers)
        
        # Meta-learning components
        self.meta_network = nn.Sequential(
            nn.Linear(input_size + 10, 64),  # +10 for meta features
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, hidden_sizes[0])  # Adaptation parameters
        )
        
        # Attention mechanism for market context
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_sizes[0], 
            num_heads=4, 
            batch_first=True
        )
        
    def forward(self, x: torch.Tensor, meta_features: Optional[torch.Tensor] = None) -> torch.Tensor:
        if meta_features is not None:
            # Meta-learning adaptation
            combined_input = torch.cat([x, meta_features], dim=-1)
            adaptation_params = self.meta_network(combined_input)
            
            # Apply adaptation to first layer
            adapted_x = x + adaptation_params
            return self.network(adapted_x)
        else:
            return self.network(x)

class ZeroShotMetaRLAgent:
    """
    Revolutionary RL agent that learns from zero training data
    Uses meta-learning to adapt to new market conditions instantly
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Network architecture (memory optimized)
        self.input_size = 60  # Market features (adjusted for actual feature count)
        self.hidden_sizes = [128, 64, 32]  # Compact for efficiency
        self.output_size = 5  # Actions: strong_buy, buy, hold, sell, strong_sell
        
        # Initialize networks
        self.policy_net = AdaptiveNeuralNetwork(
            self.input_size, self.hidden_sizes, self.output_size
        )
        self.target_net = AdaptiveNeuralNetwork(
            self.input_size, self.hidden_sizes, self.output_size
        )
        
        # Copy weights to target network
        self.target_net.load_state_dict(self.policy_net.state_dict())
        
        # Optimizer with adaptive learning rate
        self.optimizer = optim.AdamW(
            self.policy_net.parameters(), 
            lr=settings.rl.meta_learning_rate,
            weight_decay=1e-5
        )
        
        # Learning components
        self.memory = deque(maxlen=settings.rl.replay_buffer_size)
        self.meta_memory = deque(maxlen=1000)  # For meta-learning
        
        # Exploration strategy
        self.epsilon = 1.0  # Start with full exploration
        self.epsilon_min = 0.1
        self.epsilon_decay = 0.995
        
        # Meta-learning state
        self.task_contexts = {}  # Different market contexts
        self.adaptation_history = deque(maxlen=100)
        
        # Performance tracking
        self.episode_rewards = deque(maxlen=100)
        self.learning_steps = 0
        
        # Market analyzer for context
        self.market_analyzer = MarketAnalyzer()
        
        # Device setup (CPU for old PCs)
        self.device = torch.device("cpu")
        self.policy_net.to(self.device)
        self.target_net.to(self.device)
        
        self.logger.info("🧠 Zero-Shot Meta-RL Agent initialized")
    
    def extract_market_features(self, market_data: Dict[str, Any]) -> np.ndarray:
        """Extract features from market data for neural network input"""
        try:
            features = []
            
            # Price features
            if 'current_price' in market_data and market_data['current_price']:
                price_data = market_data['current_price']
                features.extend([
                    price_data.get('close', 0),
                    price_data.get('high', 0),
                    price_data.get('low', 0),
                    price_data.get('volume', 0)
                ])
            else:
                features.extend([0, 0, 0, 0])
            
            # Technical indicators (if available)
            indicators = [
                'SMA_20', 'EMA_12', 'RSI', 'MACD', 'BB_Upper', 'BB_Lower',
                'Volume_Ratio', 'MACD_Signal'
            ]
            
            for indicator in indicators:
                features.append(0)  # Placeholder - will be filled by market analyzer
            
            # Market indices features
            if 'market_indices' in market_data:
                indices_data = market_data['market_indices']
                for index_name in ['^NSEI', '^BSESN']:
                    if index_name in indices_data:
                        features.append(indices_data[index_name].get('close', 0))
                    else:
                        features.append(0)
            else:
                features.extend([0, 0])
            
            # Time-based features
            now = datetime.now()
            features.extend([
                now.hour / 24.0,  # Hour of day
                now.weekday() / 7.0,  # Day of week
                now.month / 12.0  # Month of year
            ])
            
            # Pad or truncate to exact input size
            while len(features) < self.input_size:
                features.append(0.0)
            features = features[:self.input_size]
            
            return np.array(features, dtype=np.float32)
            
        except Exception as e:
            self.logger.error(f"Error extracting market features: {e}")
            return np.zeros(self.input_size, dtype=np.float32)
    
    def extract_meta_features(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract high-level meta features for adaptation"""
        try:
            meta_features = {}
            
            # Sector information
            sector = self._get_symbol_sector(symbol)
            meta_features['sector_volatility'] = self._get_sector_volatility(sector)
            meta_features['sector_correlation'] = self._get_sector_correlation(sector)
            
            # Market regime
            meta_features['market_volatility'] = self._estimate_market_volatility(market_data)
            meta_features['market_trend'] = self._estimate_market_trend(market_data)
            
            # Time context
            now = datetime.now()
            meta_features['market_open_time'] = self._get_market_time_factor(now)
            meta_features['day_of_week'] = now.weekday() / 7.0
            
            # Recent performance context
            meta_features['recent_accuracy'] = self._get_recent_accuracy(symbol)
            meta_features['confidence_calibration'] = self._get_confidence_calibration()
            
            return meta_features
            
        except Exception as e:
            self.logger.error(f"Error extracting meta features: {e}")
            return {f'meta_{i}': 0.0 for i in range(10)}
    
    async def make_prediction(self, symbol: str, market_data: Dict[str, Any]) -> Tuple[PredictionAction, float, str]:
        """
        Make a prediction using the meta-learning agent
        
        Returns:
            (action, confidence, reasoning)
        """
        try:
            # Extract features
            state_features = self.extract_market_features(market_data)
            meta_features = self.extract_meta_features(symbol, market_data)
            
            # Convert to tensors
            state_tensor = torch.FloatTensor(state_features).unsqueeze(0).to(self.device)
            meta_tensor = torch.FloatTensor(list(meta_features.values())).unsqueeze(0).to(self.device)
            
            # Get action probabilities from network
            with torch.no_grad():
                action_values = self.policy_net(state_tensor, meta_tensor)
                action_probs = F.softmax(action_values, dim=-1)
            
            # Epsilon-greedy exploration with meta-learning adaptation
            if random.random() < self.epsilon:
                # Exploration: sample from probability distribution
                action_idx = torch.multinomial(action_probs, 1).item()
            else:
                # Exploitation: choose best action
                action_idx = action_probs.argmax().item()
            
            # Convert to PredictionAction
            actions = list(PredictionAction)
            action = actions[action_idx]
            
            # Calculate confidence
            confidence = float(action_probs[0][action_idx])
            
            # Generate reasoning using market analysis
            reasoning = await self._generate_reasoning(symbol, market_data, action, confidence)
            
            # Update exploration rate
            self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)
            
            return action, confidence, reasoning
            
        except Exception as e:
            self.logger.error(f"Error making prediction for {symbol}: {e}")
            return PredictionAction.HOLD, 0.5, "Error in prediction generation"
    
    async def _generate_reasoning(self, symbol: str, market_data: Dict[str, Any], 
                                action: PredictionAction, confidence: float) -> str:
        """Generate human-readable reasoning for the prediction"""
        try:
            reasoning_parts = []
            
            # Market context
            sector = self._get_symbol_sector(symbol)
            reasoning_parts.append(f"Analyzing {symbol} in {sector} sector")
            
            # Technical analysis
            if 'current_price' in market_data and market_data['current_price']:
                price = market_data['current_price']['close']
                reasoning_parts.append(f"Current price: ₹{price:.2f}")
            
            # Action reasoning
            action_reasoning = {
                PredictionAction.STRONG_BUY: "Strong bullish signals detected",
                PredictionAction.BUY: "Positive momentum indicators",
                PredictionAction.HOLD: "Mixed signals, maintaining position",
                PredictionAction.SELL: "Bearish indicators emerging",
                PredictionAction.STRONG_SELL: "Strong bearish signals detected"
            }
            reasoning_parts.append(action_reasoning.get(action, "Neutral outlook"))
            
            # Confidence explanation
            if confidence > 0.8:
                reasoning_parts.append("High confidence based on clear market signals")
            elif confidence > 0.6:
                reasoning_parts.append("Moderate confidence with some uncertainty")
            else:
                reasoning_parts.append("Low confidence due to mixed signals")
            
            return " | ".join(reasoning_parts)
            
        except Exception as e:
            self.logger.error(f"Error generating reasoning: {e}")
            return f"Prediction: {action.name} with {confidence:.2f} confidence"
    
    def learn_from_experience(self, experiences: List[Dict[str, Any]]):
        """Learn from resolved predictions using meta-learning"""
        try:
            if len(experiences) < settings.rl.meta_batch_size:
                return
            
            # Convert experiences to training data
            states = []
            actions = []
            rewards = []
            next_states = []
            dones = []
            meta_features_list = []
            
            for exp in experiences:
                # Extract state features
                state_features = self.extract_market_features(exp['state'])
                states.append(state_features)
                
                actions.append(exp['action'])
                rewards.append(exp['reward'])
                dones.append(exp['done'])
                
                # Extract next state features
                next_state_features = self.extract_market_features(exp.get('next_state', {}))
                next_states.append(next_state_features)
                
                # Extract meta features
                symbol = exp['info']['symbol']
                meta_features = self.extract_meta_features(symbol, exp['state'])
                meta_features_list.append(list(meta_features.values()))
            
            # Convert to tensors
            states_tensor = torch.FloatTensor(states).to(self.device)
            actions_tensor = torch.LongTensor(actions).to(self.device)
            rewards_tensor = torch.FloatTensor(rewards).to(self.device)
            next_states_tensor = torch.FloatTensor(next_states).to(self.device)
            dones_tensor = torch.BoolTensor(dones).to(self.device)
            meta_tensor = torch.FloatTensor(meta_features_list).to(self.device)
            
            # Compute current Q values
            current_q_values = self.policy_net(states_tensor, meta_tensor)
            current_q_values = current_q_values.gather(1, actions_tensor.unsqueeze(1))
            
            # Compute next Q values using target network
            with torch.no_grad():
                next_q_values = self.target_net(next_states_tensor, meta_tensor)
                max_next_q_values = next_q_values.max(1)[0]
                target_q_values = rewards_tensor + (0.99 * max_next_q_values * ~dones_tensor)
            
            # Compute loss
            loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
            
            # Meta-learning: adapt learning rate based on recent performance
            recent_rewards = [exp['reward'] for exp in experiences[-10:]]
            if len(recent_rewards) > 5:
                avg_recent_reward = np.mean(recent_rewards)
                if avg_recent_reward < 0:
                    # Increase learning rate if performing poorly
                    for param_group in self.optimizer.param_groups:
                        param_group['lr'] = min(param_group['lr'] * 1.1, 0.01)
                elif avg_recent_reward > 0.5:
                    # Decrease learning rate if performing well
                    for param_group in self.optimizer.param_groups:
                        param_group['lr'] = max(param_group['lr'] * 0.95, 1e-5)
            
            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(self.policy_net.parameters(), 1.0)
            
            self.optimizer.step()
            
            # Update target network periodically
            self.learning_steps += 1
            if self.learning_steps % 100 == 0:
                self.target_net.load_state_dict(self.policy_net.state_dict())
            
            # Log learning progress
            self.logger.info(f"🎓 Learning step {self.learning_steps} | "
                           f"Loss: {loss.item():.4f} | "
                           f"Avg Reward: {np.mean(rewards):.3f}")
            
        except Exception as e:
            self.logger.error(f"Error in learning from experience: {e}")
    
    def _get_symbol_sector(self, symbol: str) -> str:
        """Get sector for a symbol"""
        from config.indian_stocks import SECTORS
        
        for sector, data in SECTORS.items():
            if symbol in data['stocks']:
                return sector
        return "UNKNOWN"
    
    def _get_sector_volatility(self, sector: str) -> float:
        """Get volatility characteristic for sector"""
        volatility_map = {
            "IT": 0.3, "BANKING": 0.7, "PHARMA": 0.8, "AUTO": 0.7,
            "FMCG": 0.2, "ENERGY": 0.9, "METALS": 0.9, "TELECOM": 0.5
        }
        return volatility_map.get(sector, 0.5)
    
    def _get_sector_correlation(self, sector: str) -> float:
        """Get global correlation for sector"""
        correlation_map = {
            "IT": 0.8, "BANKING": 0.4, "PHARMA": 0.4, "AUTO": 0.4,
            "FMCG": 0.2, "ENERGY": 0.9, "METALS": 0.9, "TELECOM": 0.2
        }
        return correlation_map.get(sector, 0.5)
    
    def _estimate_market_volatility(self, market_data: Dict[str, Any]) -> float:
        """Estimate current market volatility"""
        # Simplified volatility estimation
        return 0.5  # Placeholder
    
    def _estimate_market_trend(self, market_data: Dict[str, Any]) -> float:
        """Estimate market trend direction"""
        # Simplified trend estimation
        return 0.0  # Placeholder
    
    def _get_market_time_factor(self, timestamp: datetime) -> float:
        """Get market timing factor"""
        hour = timestamp.hour
        if 9 <= hour <= 15:  # Market hours
            return 1.0
        else:
            return 0.0
    
    def _get_recent_accuracy(self, symbol: str) -> float:
        """Get recent accuracy for this symbol"""
        # Get from live environment
        performance = live_env.get_performance_summary()
        symbol_perf = performance.get('top_performing_symbols', [])
        
        for sym, perf in symbol_perf:
            if sym == symbol:
                return perf.get('accuracy', 0.5)
        
        return 0.5  # Default
    
    def _get_confidence_calibration(self) -> float:
        """Get confidence calibration metric"""
        performance = live_env.get_performance_summary()
        insights = performance.get('learning_insights', {})
        
        if 'confidence_calibration' in insights:
            return insights['confidence_calibration'].get('high_confidence_accuracy', 0.5)
        
        return 0.5  # Default
    
    def save_model(self, path: str):
        """Save the trained model"""
        try:
            torch.save({
                'policy_net_state_dict': self.policy_net.state_dict(),
                'target_net_state_dict': self.target_net.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'learning_steps': self.learning_steps,
                'epsilon': self.epsilon
            }, path)
            
            self.logger.info(f"💾 Model saved to {path}")
            
        except Exception as e:
            self.logger.error(f"Error saving model: {e}")
    
    def load_model(self, path: str):
        """Load a trained model"""
        try:
            checkpoint = torch.load(path, map_location=self.device)
            
            self.policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
            self.target_net.load_state_dict(checkpoint['target_net_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.learning_steps = checkpoint['learning_steps']
            self.epsilon = checkpoint['epsilon']
            
            self.logger.info(f"📂 Model loaded from {path}")
            
        except Exception as e:
            self.logger.error(f"Error loading model: {e}")

# Global agent instance
meta_agent = ZeroShotMetaRLAgent()
